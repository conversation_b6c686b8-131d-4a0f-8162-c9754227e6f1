using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Azure.Storage.Blobs;
using Azure.Identity;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Graph;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using System.Text.Json;

var host = new HostBuilder()
    .ConfigureFunctionsWorkerDefaults()
    .ConfigureServices((context, services) =>
    {
        var configuration = context.Configuration;

        services.AddLogging(builder =>
        {
            builder.AddConsole();
            builder.AddApplicationInsights();
        });

        services.AddMemoryCache();
        services.AddSingleton<IConfiguration>(configuration);

        // Configure strongly-typed options
        services.Configure<SendGridOptions>(configuration.GetSection(SendGridOptions.SectionName));
        services.Configure<EntraOptions>(options =>
        {
            options.ClientId = configuration["EntraExternalID:ClientId"] ?? string.Empty;
            options.ClientSecret = configuration["EntraExternalID:ClientSecret"] ?? string.Empty;
            options.TenantId = configuration["EntraExternalID:TenantId"] ?? string.Empty;
            options.DefaultDomain = configuration["EntraExternalID:DefaultDomain"] ?? "yourtenant.onmicrosoft.com";
        });
        services.Configure<PasswordResetOptions>(configuration.GetSection(PasswordResetOptions.SectionName));
        services.Configure<AccountRegistrationOptions>(configuration.GetSection(AccountRegistrationOptions.SectionName));
        services.Configure<StorageOptions>(options =>
        {
            // Use AzureWebJobsStorage as primary (always available in Azure Functions)
            // Fall back to Storage:ConnectionString for local development
            options.ConnectionString = configuration["AzureWebJobsStorage"] ?? configuration["Storage:ConnectionString"] ?? string.Empty;
        });
        services.Configure<RateLimitOptions>(configuration.GetSection(RateLimitOptions.SectionName));
        services.Configure<InvitationOptions>(configuration.GetSection(InvitationOptions.SectionName));

        // Azure Blob Storage client
        services.AddSingleton<BlobServiceClient>(serviceProvider =>
        {
            var storageOptions = serviceProvider.GetRequiredService<IOptions<StorageOptions>>();
            var connectionString = storageOptions.Value.ConnectionString;

            if (string.IsNullOrEmpty(connectionString))
            {
                throw new InvalidOperationException("Storage:ConnectionString is required in configuration. Please configure this value in local.settings.json.");
            }

            return new BlobServiceClient(connectionString);
        });

        // HTTP client factory
        services.AddHttpClient();

        // Core services
        services.AddScoped<IPasswordHistoryService, PasswordHistoryService>();
        services.AddScoped<IEmailService, EmailService>();

        // Graph API client
        services.AddScoped<GraphServiceClient>(serviceProvider =>
        {
            var entraOptions = serviceProvider.GetRequiredService<IOptions<EntraOptions>>();
            var clientId = entraOptions.Value.ClientId;
            var clientSecret = entraOptions.Value.ClientSecret;
            var tenantId = entraOptions.Value.TenantId;

            if (string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret) || string.IsNullOrEmpty(tenantId))
            {
                throw new InvalidOperationException("EntraExternalID configuration (ClientId, ClientSecret, TenantId) is required in configuration. Please configure these values in local.settings.json.");
            }

            var credential = new ClientSecretCredential(tenantId, clientId, clientSecret);
            return new GraphServiceClient(credential);
        });

        services.AddScoped<RateLimitHelper>();
        services.AddScoped<ResetTokenManager>();
        services.AddScoped<InvitationTokenManager>();

        services.AddScoped<PasswordHistoryValidator.AuthenticationFunction>();
        services.AddScoped<PasswordHistoryValidator.PasswordFunction>();
        services.AddScoped<PasswordHistoryValidator.UtilityFunction>();
        services.AddScoped<PasswordHistoryValidator.InvitationFunction>();


        // JSON serializer options as singleton
        services.AddSingleton<JsonSerializerOptions>(provider => new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            PropertyNameCaseInsensitive = true,
            WriteIndented = true
        });
    })
    .ConfigureAppConfiguration((context, config) =>
    {
        config.AddJsonFile("local.settings.json", optional: true, reloadOnChange: true);
        config.AddEnvironmentVariables();
        var keyVaultUrl = Environment.GetEnvironmentVariable("KeyVaultUrl");
        if (!string.IsNullOrEmpty(keyVaultUrl))
        {
            config.AddAzureKeyVault(new Uri(keyVaultUrl), new DefaultAzureCredential());
        }
    })
    .Build();

host.Run();
