using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;

namespace PasswordHistoryValidator.Shared;


public class RateLimitHelper
{
    private readonly IMemoryCache _cache;
    private readonly ILogger<RateLimitHelper> _logger;
    private readonly int _maxRequestsPerMinute;
    private readonly TimeSpan _windowDuration = TimeSpan.FromMinutes(1);

    public RateLimitHelper(IMemoryCache cache, ILogger<RateLimitHelper> logger, IOptions<RateLimitOptions> rateLimitOptions)
    {
        _cache = cache;
        _logger = logger;
        _maxRequestsPerMinute = rateLimitOptions.Value.MaxRequestsPerMinute;
    }


    public bool IsRequestAllowed(string clientId, string operation)
    {
        var key = $"rate_limit_{clientId}_{operation}";
        var now = DateTime.UtcNow;

        if (_cache.TryGetValue(key, out RateLimitData? data))
        {
            if (now > data!.WindowEnd)
            {

                data = new RateLimitData
                {
                    Count = 1,
                    WindowStart = now,
                    WindowEnd = now.Add(_windowDuration)
                };
                _cache.Set(key, data, _windowDuration);
                return true;
            }


            if (data.Count >= _maxRequestsPerMinute)
            {
                _logger.LogWarning("Rate limit exceeded for client {ClientId} operation {Operation}. Count: {Count}, Limit: {Limit}",
                    clientId, operation, data.Count, _maxRequestsPerMinute);
                return false;
            }


            data.Count++;
            _cache.Set(key, data, data.WindowEnd - now);
            return true;
        }
        else
        {

            var newData = new RateLimitData
            {
                Count = 1,
                WindowStart = now,
                WindowEnd = now.Add(_windowDuration)
            };
            _cache.Set(key, newData, _windowDuration);
            return true;
        }
    }


    public RateLimitInfo GetRateLimitInfo(string clientId, string operation)
    {
        var key = $"rate_limit_{clientId}_{operation}";
        var now = DateTime.UtcNow;

        if (_cache.TryGetValue(key, out RateLimitData? data))
        {
            if (now > data!.WindowEnd)
            {
                return new RateLimitInfo
                {
                    IsAllowed = true,
                    CurrentCount = 0,
                    MaxRequests = _maxRequestsPerMinute,
                    WindowDuration = _windowDuration,
                    WindowResetTime = now.Add(_windowDuration)
                };
            }

            return new RateLimitInfo
            {
                IsAllowed = data.Count < _maxRequestsPerMinute,
                CurrentCount = data.Count,
                MaxRequests = _maxRequestsPerMinute,
                WindowDuration = _windowDuration,
                WindowResetTime = data.WindowEnd
            };
        }

        return new RateLimitInfo
        {
            IsAllowed = true,
            CurrentCount = 0,
            MaxRequests = _maxRequestsPerMinute,
            WindowDuration = _windowDuration,
            WindowResetTime = now.Add(_windowDuration)
        };
    }


    public string GetHealthStatus()
    {
        try
        {

            var testKey = $"health_check_{Guid.NewGuid()}";
            _cache.Set(testKey, "test", TimeSpan.FromSeconds(1));
            var retrieved = _cache.Get(testKey);
            return retrieved != null ? "healthy" : "degraded";
        }
        catch
        {
            return "unhealthy";
        }
    }
}

public class RateLimitInfo
{
    public bool IsAllowed { get; set; }
    public int CurrentCount { get; set; }
    public int MaxRequests { get; set; }
    public TimeSpan WindowDuration { get; set; }
    public DateTime WindowResetTime { get; set; }
}

internal class RateLimitData
{
    public int Count { get; set; }
    public DateTime WindowStart { get; set; }
    public DateTime WindowEnd { get; set; }
}
