using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using BCrypt.Net;

namespace PasswordHistoryValidator;

public partial class PasswordFunction : BaseFunctionService
{
    private readonly ILogger<PasswordFunction> _logger;
    private readonly IPasswordHistoryService _passwordHistoryService;
    private readonly RateLimitHelper _rateLimitHelper;
    private readonly ResetTokenManager _resetTokenManager;
    private readonly EmailService _emailService;
    private readonly GraphServiceClient _graphServiceClient;

    public PasswordFunction(
        ILogger<PasswordFunction> logger,
        IPasswordHistoryService passwordHistoryService,
        RateLimitHelper rateLimitHelper,
        ResetTokenManager resetTokenManager,
        EmailService emailService,
        GraphServiceClient graphServiceClient,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _passwordHistoryService = passwordHistoryService;
        _rateLimitHelper = rateLimitHelper;
        _resetTokenManager = resetTokenManager;
        _emailService = emailService;
        _graphServiceClient = graphServiceClient;
    }

    [Function("PasswordService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = GenerateCorrelationId();

        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await CreateErrorResponse(req, "Operation parameter required", correlationId);
            }

            return operation.ToLower() switch
            {
                "validate" => await HandlePasswordValidation(req, correlationId, cancellationToken),
                "update-history" => await HandleHistoryUpdate(req, correlationId, cancellationToken),
                "reset-initiate" => await HandleResetInitiate(req, correlationId, cancellationToken),
                "reset-complete" => await HandleResetComplete(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Password service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }



    private async Task<HttpResponseData> HandlePasswordValidation(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonSerializer.Deserialize<PasswordRequest>(requestBody, JsonOptions);

            if (data == null)
            {
                return await CreateErrorResponse(req, "Invalid request data", correlationId);
            }

            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(data);
            if (!Validator.TryValidateObject(data, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
            }

            var clientId = BaseFunctionService.GetClientIdentifier(req);
            var rateLimitInfo = _rateLimitHelper.GetRateLimitInfo(clientId, "validate");
            if (!rateLimitInfo.IsAllowed)
            {
                return await CreateJsonResponse(req, new
                {
                    success = false,
                    message = "Rate limit exceeded. Please try again later.",
                    errorCode = "RateLimitExceeded",
                    retryAfter = rateLimitInfo.WindowResetTime
                }, HttpStatusCode.TooManyRequests, correlationId);
            }



            if (string.IsNullOrEmpty(data.UserId) || string.IsNullOrEmpty(data.NewPassword))
            {
                return await CreateErrorResponse(req, "UserId and NewPassword are required for validation", correlationId);
            }

            var validationResult = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
                data.ApplicationName, data.UserId, data.NewPassword, cancellationToken);

            if (!validationResult.IsSuccess)
            {
                if (validationResult.ErrorCode == ErrorCodes.PasswordInHistory)
                {
                    _logger.LogWarning("Password reuse detected for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
                    return await CreateJsonResponse(req, new
                    {
                        version = "1.0.0",
                        action = "ShowBlockPage",
                        userMessage = validationResult.ErrorMessage
                    }, HttpStatusCode.Conflict, correlationId);
                }
                else
                {
                    _logger.LogError("Password validation failed for {Email}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                        data.Email, validationResult.ErrorMessage, correlationId);
                    return await CreateErrorResponse(req, "Error accessing password history. Please try again later.", correlationId);
                }
            }

            // Password is valid, now update the password history to prevent bypass
            var updateResult = await _passwordHistoryService.UpdatePasswordHistoryAsync(
                data.ApplicationName, data.UserId, data.NewPassword, cancellationToken);

            if (!updateResult.IsSuccess)
            {
                _logger.LogError("Failed to update password history for {Email}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                    data.Email, updateResult.ErrorMessage, correlationId);
                return await CreateJsonResponse(req, new
                {
                    version = "1.0.0",
                    action = "Continue",
                    userMessage = "Password validation successful. History update will be retried.",
                    metadata = new
                    {
                        email = data.Email,
                        validationTimestamp = DateTime.UtcNow,
                        historyUpdated = false
                    }
                }, HttpStatusCode.OK, correlationId);
            }

            return await CreateJsonResponse(req, new
            {
                version = "1.0.0",
                action = "Continue",
                userMessage = "Password validation successful",
                metadata = new
                {
                    email = data.Email,
                    validationTimestamp = DateTime.UtcNow,
                    historyUpdated = true
                }
            }, HttpStatusCode.OK, correlationId);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during password validation [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Password validation failed. Please try again.", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleHistoryUpdate(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonSerializer.Deserialize<UpdatePasswordHistoryRequest>(requestBody, JsonOptions);

            if (data == null)
            {
                return await CreateErrorResponse(req, "Invalid request data", correlationId);
            }

            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(data);
            if (!Validator.TryValidateObject(data, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
            }

            var clientId = BaseFunctionService.GetClientIdentifier(req);
            var rateLimitInfo = _rateLimitHelper.GetRateLimitInfo(clientId, "update-history");
            if (!rateLimitInfo.IsAllowed)
            {
                return await CreateJsonResponse(req, new
                {
                    success = false,
                    message = "Rate limit exceeded. Please try again later.",
                    errorCode = "RateLimitExceeded",
                    retryAfter = rateLimitInfo.WindowResetTime
                }, HttpStatusCode.TooManyRequests, correlationId);
            }

            var userId = !string.IsNullOrEmpty(data.UserId) ? data.UserId : data.Email;



            var updateResult = await _passwordHistoryService.UpdatePasswordHistoryAsync(
                data.ApplicationName, userId, data.NewPassword, cancellationToken);

            if (!updateResult.IsSuccess)
            {
                _logger.LogError("Failed to update password history for user {UserId}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                    userId, updateResult.ErrorMessage, correlationId);
                return await CreateErrorResponse(req, updateResult.ErrorMessage, correlationId);
            }



            return await CreateJsonResponse(req, new { success = true, message = "Password history updated successfully" }, HttpStatusCode.OK, correlationId);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during history update [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "History update failed. Please try again.", correlationId);
        }
    }

    // Handle forgot password initiation
    private async Task<HttpResponseData> HandleResetInitiate(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            // Read and validate request
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonSerializer.Deserialize<PasswordRequest>(requestBody, JsonOptions);

            if (data == null)
            {
                return await CreateErrorResponse(req, "Invalid request data", correlationId);
            }

            // Validate request data
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(data);
            if (!Validator.TryValidateObject(data, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
            }

            // Rate limiting check
            var clientId = BaseFunctionService.GetClientIdentifier(req);
            var rateLimitInfo = _rateLimitHelper.GetRateLimitInfo(clientId, "reset-initiate");
            if (!rateLimitInfo.IsAllowed)
            {
                return await CreateJsonResponse(req, new
                {
                    success = false,
                    message = "Rate limit exceeded. Please try again later.",
                    errorCode = "RateLimitExceeded",
                    retryAfter = rateLimitInfo.WindowResetTime
                }, HttpStatusCode.TooManyRequests, correlationId);
            }

            // Check if user exists in application
            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter =
                        $"(mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')) and startswith(department, '{data.ApplicationName}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id" };
                    requestConfiguration.QueryParameters.Top = 1;
                }, cancellationToken);
            var userExists = users?.Value?.Count > 0;

            if (userExists)
            {
                var resetToken = _resetTokenManager.GenerateSecureToken();
                var verificationCode = _resetTokenManager.StoreResetToken(data.ApplicationName, data.Email!, resetToken);
                await _resetTokenManager.SendResetEmail(data.Email!, resetToken, verificationCode, data.ApplicationName, correlationId);

            }

            // Always return success message for security (don't reveal if email exists)
            return await CreateJsonResponse(req, new
            {
                success = true,
                message = "If an account with that email exists, you will receive a password reset link shortly. Please check your email."
            }, HttpStatusCode.OK, correlationId);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during reset initiation [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Reset initiation failed. Please try again.", correlationId);
        }
    }

    // Handle password reset/change operations (unified)
    private async Task<HttpResponseData> HandleResetComplete(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            // Read and validate request
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonSerializer.Deserialize<PasswordRequest>(requestBody, JsonOptions);

            if (data == null)
            {
                return await CreateErrorResponse(req, "Invalid request data", correlationId);
            }



            // Determine operation type: token-based reset vs current password change
            bool hasToken = !string.IsNullOrEmpty(data.Token);
            bool hasVerificationCode = !string.IsNullOrEmpty(data.VerificationCode);
            bool hasCurrentPassword = !string.IsNullOrEmpty(data.CurrentPassword);
            bool hasEmail = !string.IsNullOrEmpty(data.Email);

            bool isTokenBasedReset = hasToken && hasVerificationCode;
            bool isPasswordChange = hasCurrentPassword && hasEmail;

            if (!isTokenBasedReset && !isPasswordChange)
            {
                return await CreateErrorResponse(req, "Either (token + verification code) for password reset OR (current password + email) for password change is required", correlationId);
            }

            // For token-based reset, validate token and verification code
            if (isTokenBasedReset)
            {
                var (isValid, tokenData, errorMessage) = _resetTokenManager.ValidateResetToken(data.Token!, data.VerificationCode!);
                if (!isValid)
                {
                    _logger.LogWarning("Invalid or expired reset token/verification code for application {ApplicationName} [CorrelationId: {CorrelationId}]",
                        data.ApplicationName, correlationId);
                    return await CreateErrorResponse(req, errorMessage, correlationId);
                }

                // For token-based reset, use email from token data
                data.Email = tokenData!.Email;

            }

            // Determine if this is a password reset (token-based) or password change (current password required)
            bool isPasswordReset = isTokenBasedReset;

            // Rate limiting check
            var clientId = BaseFunctionService.GetClientIdentifier(req);
            var operationType = isPasswordReset ? "reset-complete" : "password-change";
            var rateLimitInfo = _rateLimitHelper.GetRateLimitInfo(clientId, operationType);
            if (!rateLimitInfo.IsAllowed)
            {
                return await CreateJsonResponse(req, new
                {
                    success = false,
                    message = "Rate limit exceeded. Please try again later.",
                    errorCode = "RateLimitExceeded",
                    retryAfter = rateLimitInfo.WindowResetTime
                }, HttpStatusCode.TooManyRequests, correlationId);
            }

            // Process secure password operation with current password verification using email identification
            var operation = isPasswordReset ? "password reset" : "password change";


            // Use email-based identification with application context
            string userEmail = data.Email!;

            // Resolve email to User ID using Graph API
            var emailFilter = $"mail eq '{userEmail}' or userPrincipalName eq '{userEmail}' or proxyAddresses/any(c:c eq 'SMTP:{userEmail}')";
            var filter = $"({emailFilter}) and startswith(department, '{data.ApplicationName}')";

            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter = filter;
                    requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName", "department" };
                }, cancellationToken);

            var user = users?.Value?.FirstOrDefault();
            if (user == null)
            {
                _logger.LogWarning("Failed to resolve email {Email} to User ID for application {ApplicationName} [CorrelationId: {CorrelationId}]",
                    userEmail, data.ApplicationName, correlationId);
                return await CreateErrorResponse(req, "User not found with the provided email address in the specified application context.", correlationId);
            }

            string resolvedUserId = user.Id ?? string.Empty;


            if (string.IsNullOrEmpty(data.NewPassword))
            {
                return await CreateErrorResponse(req, "NewPassword is required", correlationId);
            }

            // Validate new password against history
            var passwordValidation = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
                data.ApplicationName, resolvedUserId, data.NewPassword, cancellationToken);

            if (!passwordValidation.IsSuccess)
            {
                _logger.LogWarning("Password validation failed for {Email}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                    userEmail, passwordValidation.ErrorMessage, correlationId);
                return await CreateErrorResponse(req, passwordValidation.ErrorMessage, correlationId);
            }

            // Use appropriate method based on operation type
            try
            {
                if (isPasswordReset)
                {
                    // For token-based password reset, update password directly (no current password required)
                    var userUpdate = new User
                    {
                        PasswordProfile = new PasswordProfile
                        {
                            Password = data.NewPassword,
                            ForceChangePasswordNextSignIn = false
                        }
                    };
                    await _graphServiceClient.Users[resolvedUserId].PatchAsync(userUpdate, requestConfiguration => { }, cancellationToken);
                }
                else
                {
                    // For password change, validate current password first
                    var historyResult = await _passwordHistoryService.GetPasswordHistoryAsync(data.ApplicationName, resolvedUserId, cancellationToken);
                    if (!historyResult.IsSuccess || !historyResult.Value?.Any(hash => BCrypt.Net.BCrypt.Verify(data.CurrentPassword, hash)) != true)
                    {
                        _logger.LogWarning("Current password validation failed for {Email} [CorrelationId: {CorrelationId}]", userEmail, correlationId);
                        return await CreateErrorResponse(req, "Current password is incorrect. Please verify your current password and try again.", correlationId);
                    }

                    // Update password directly
                    var userUpdate = new User
                    {
                        PasswordProfile = new PasswordProfile
                        {
                            Password = data.NewPassword,
                            ForceChangePasswordNextSignIn = false
                        }
                    };
                    await _graphServiceClient.Users[resolvedUserId].PatchAsync(userUpdate, requestConfiguration => { }, cancellationToken);
                }

                // Send password changed notification
                await _emailService.SendPasswordChangedNotificationAsync(userEmail, correlationId, cancellationToken);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Password update failed for {Email} [CorrelationId: {CorrelationId}]", userEmail, correlationId);
                return await CreateErrorResponse(req, "Password update failed", correlationId);
            }

            // Update password history
            var historyUpdate = await _passwordHistoryService.UpdatePasswordHistoryAsync(
                data.ApplicationName, resolvedUserId, data.NewPassword, cancellationToken);

            if (!historyUpdate.IsSuccess)
            {
                _logger.LogError("Password updated but failed to update password history for {Email}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                    userEmail, historyUpdate.ErrorMessage, correlationId);
                // Password was updated successfully, so we don't fail the request
            }

            // Mark token as used for token-based password reset
            if (isPasswordReset && !string.IsNullOrEmpty(data.Token))
            {
                _resetTokenManager.MarkTokenAsUsed(data.Token);

            }

            var operationName = isPasswordReset ? "reset" : "changed";


            var successMessage = isPasswordReset
                ? "Password reset successfully. You will be logged out and need to sign in with your new password."
                : "Password changed successfully.";

            return await CreateJsonResponse(req, new
            {
                message = successMessage,
                email = userEmail,
                requiresLogout = isPasswordReset // Indicate if user should be logged out
            }, HttpStatusCode.OK, correlationId);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during password operation [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Password operation failed. Please try again.", correlationId);
        }
    }

}
