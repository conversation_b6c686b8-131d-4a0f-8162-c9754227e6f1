using Azure.Storage.Blobs;
using Azure.Storage.Blobs.Models;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using System.Text;
using System.Text.Json;
using System.Text.Json.Serialization;

namespace PasswordHistoryValidator.Services;

public class PasswordHistoryService : IPasswordHistoryService
{
    private readonly ILogger<PasswordHistoryService> _logger;
    private readonly BlobServiceClient _blobServiceClient;
    private readonly IMemoryCache _cache;
    private readonly JsonSerializerOptions _jsonOptions;
    private readonly int _maxHistoryCount;
    private readonly int _workFactor;

    private const string ContainerName = "passwordhistory";
    private readonly TimeSpan _cacheExpiration = TimeSpan.FromMinutes(15);

    public PasswordHistoryService(
        ILogger<PasswordHistoryService> logger,
        BlobServiceClient blobServiceClient,
        IMemoryCache cache,
        IConfiguration configuration,
        JsonSerializerOptions jsonOptions)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _blobServiceClient = blobServiceClient ?? throw new ArgumentNullException(nameof(blobServiceClient));
        _cache = cache ?? throw new ArgumentNullException(nameof(cache));
        _jsonOptions = jsonOptions ?? throw new ArgumentNullException(nameof(jsonOptions));
        
        // Cache configuration values
        _maxHistoryCount = configuration.GetValue<int>("PasswordHistory:MaxCount", 12);
        _workFactor = configuration.GetValue<int>("PasswordHistory:WorkFactor", 12);
    }

    // Core implementation methods
    public async Task<Result<List<string>>> GetPasswordHistoryAsync(string userId, CancellationToken cancellationToken = default)
    {
        return await GetPasswordHistoryInternalAsync(userId, null, cancellationToken);
    }

    public async Task<Result<bool>> UpdatePasswordHistoryAsync(string userId, string newPassword, CancellationToken cancellationToken = default)
    {
        return await UpdatePasswordHistoryInternalAsync(userId, newPassword, null, cancellationToken);
    }

    public async Task<Result<bool>> ValidatePasswordAgainstHistoryAsync(string userId, string password, CancellationToken cancellationToken = default)
    {
        return await ValidatePasswordAgainstHistoryInternalAsync(userId, password, null, cancellationToken);
    }
    // Application-scoped overloads
    public async Task<Result<List<string>>> GetPasswordHistoryAsync(string applicationId, string userId, CancellationToken cancellationToken = default)
    {
        return await GetPasswordHistoryInternalAsync(userId, applicationId, cancellationToken);
    }

    public async Task<Result<bool>> UpdatePasswordHistoryAsync(string applicationId, string userId, string newPassword, CancellationToken cancellationToken = default)
    {
        return await UpdatePasswordHistoryInternalAsync(userId, newPassword, applicationId, cancellationToken);
    }

    public async Task<Result<bool>> ValidatePasswordAgainstHistoryAsync(string applicationId, string userId, string password, CancellationToken cancellationToken = default)
    {
        return await ValidatePasswordAgainstHistoryInternalAsync(userId, password, applicationId, cancellationToken);
    }

    // Internal implementation methods
    private async Task<Result<List<string>>> GetPasswordHistoryInternalAsync(string userId, string? applicationId, CancellationToken cancellationToken)
    {
        return await ExecuteWithErrorHandling(async () =>
        {
            var scopedUserId = GetScopedUserId(userId, applicationId);
            var cacheKey = $"password_history_{scopedUserId}";
            
            if (_cache.TryGetValue(cacheKey, out List<string>? cachedHistory))
            {
                return Result<List<string>>.Success(cachedHistory ?? new List<string>());
            }

            var blobClient = await GetBlobClientAsync(scopedUserId, cancellationToken);
            
            if (!await blobClient.ExistsAsync(cancellationToken: cancellationToken))
            {
                var emptyHistory = new List<string>();
                _cache.Set(cacheKey, emptyHistory, TimeSpan.FromMinutes(5));
                return Result<List<string>>.Success(emptyHistory);
            }

            var downloadResult = await blobClient.DownloadContentAsync(cancellationToken: cancellationToken);
            var historyData = JsonSerializer.Deserialize<PasswordHistoryStorage>(downloadResult.Value.Content.ToString(), _jsonOptions);
            var passwordHashes = historyData?.PasswordHashes ?? new List<string>();

            _cache.Set(cacheKey, passwordHashes, _cacheExpiration);
            return Result<List<string>>.Success(passwordHashes);
        }, $"Error retrieving password history for user {userId}");
    }

    private async Task<Result<bool>> UpdatePasswordHistoryInternalAsync(string userId, string newPassword, string? applicationId, CancellationToken cancellationToken)
    {
        return await ExecuteWithErrorHandling(async () =>
        {
            var scopedUserId = GetScopedUserId(userId, applicationId);
            var historyResult = await GetPasswordHistoryInternalAsync(userId, applicationId, cancellationToken);
            if (!historyResult.IsSuccess)
            {
                return Result<bool>.Failure(historyResult.ErrorMessage, historyResult.ErrorCode ?? "UNKNOWN_ERROR", Guid.NewGuid().ToString());
            }

            var passwordHashes = historyResult.Value ?? new List<string>();
            string newPasswordHash = BCrypt.Net.BCrypt.HashPassword(newPassword, _workFactor);

            passwordHashes.Insert(0, newPasswordHash);
            if (passwordHashes.Count > _maxHistoryCount)
            {
                passwordHashes = passwordHashes.Take(_maxHistoryCount).ToList();
            }

            await SavePasswordHistoryAsync(scopedUserId, passwordHashes, cancellationToken);
            return Result<bool>.Success(true);
        }, $"Error updating password history for user {userId}");
    }

    private async Task<Result<bool>> ValidatePasswordAgainstHistoryInternalAsync(string userId, string password, string? applicationId, CancellationToken cancellationToken)
    {
        return await ExecuteWithErrorHandling(async () =>
        {
            var historyResult = await GetPasswordHistoryInternalAsync(userId, applicationId, cancellationToken);
            if (!historyResult.IsSuccess)
            {
                return Result<bool>.Failure(historyResult.ErrorMessage, historyResult.ErrorCode ?? "UNKNOWN_ERROR", Guid.NewGuid().ToString());
            }

            var passwordHashes = historyResult.Value ?? new List<string>();
            foreach (var hash in passwordHashes)
            {
                if (!string.IsNullOrEmpty(hash) && BCrypt.Net.BCrypt.Verify(password, hash))
                {
                    _logger.LogWarning("Password reuse detected for user {userId}", userId);
                    return Result<bool>.Failure(
                        "Password has been used recently. Please choose a different password.",
                        ErrorCodes.PasswordInHistory,
                        Guid.NewGuid().ToString());
                }
            }

            return Result<bool>.Success(true);
        }, $"Error validating password for user {userId}");
    }

    // Helper methods
    private async Task<BlobClient> GetBlobClientAsync(string userId, CancellationToken cancellationToken)
    {
        var containerClient = _blobServiceClient.GetBlobContainerClient(ContainerName);
        await containerClient.CreateIfNotExistsAsync(PublicAccessType.None, cancellationToken: cancellationToken);
        return containerClient.GetBlobClient($"{userId}.json");
    }

    private async Task SavePasswordHistoryAsync(string userId, List<string> passwordHashes, CancellationToken cancellationToken)
    {
        var blobClient = await GetBlobClientAsync(userId, cancellationToken);
        var historyData = new PasswordHistoryStorage
        {
            UserId = userId,
            LastUpdatedUtc = DateTime.UtcNow,
            PasswordHashes = passwordHashes
        };

        var jsonHistory = JsonSerializer.Serialize(historyData, _jsonOptions);
        using var stream = new MemoryStream(Encoding.UTF8.GetBytes(jsonHistory));
        
        var options = new BlobUploadOptions
        {
            Metadata = CreateBlobMetadata(passwordHashes.Count),
            HttpHeaders = new BlobHttpHeaders { ContentType = "application/json" }
        };

        await blobClient.UploadAsync(stream, options, cancellationToken);
        
        var cacheKey = $"password_history_{userId}";
        _cache.Set(cacheKey, passwordHashes, _cacheExpiration);
    }

    private Dictionary<string, string> CreateBlobMetadata(int recordCount)
    {
        return new Dictionary<string, string>
        {
            { "lastUpdated", DateTime.UtcNow.ToString("o") },
            { "recordCount", recordCount.ToString() },
            { "correlationId", Guid.NewGuid().ToString() }
        };
    }

    private string GetScopedUserId(string userId, string? applicationId)
    {
        if (string.IsNullOrEmpty(applicationId))
            return userId;

        var sanitizedAppId = applicationId
            .Replace("/", "")
            .Replace("\\", "")
            .Replace("..", "")
            .Replace(":", "")
            .Trim();

        if (string.IsNullOrEmpty(sanitizedAppId))
            throw new ArgumentException("Application ID cannot be empty or contain only invalid characters", nameof(applicationId));

        return $"{sanitizedAppId}/{userId}";
    }

    private async Task<Result<T>> ExecuteWithErrorHandling<T>(Func<Task<Result<T>>> operation, string errorContext)
    {
        try
        {
            return await operation();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, errorContext);
            return Result<T>.Failure(
                "An error occurred while processing the request",
                ErrorCodes.StorageError,
                Guid.NewGuid().ToString());
        }
    }
}

public class PasswordHistoryStorage
{
    [JsonPropertyName("userId")]
    public string UserId { get; set; } = string.Empty;

    [JsonPropertyName("lastUpdatedUtc")]
    public DateTime LastUpdatedUtc { get; set; }

    [JsonPropertyName("passwordHashes")]
    public List<string> PasswordHashes { get; set; } = new();
}
