# Invitation-Based User Management System Guide

## Overview

This system provides a secure, invitation-based user registration flow for Power Pages applications. Users can only register after receiving a valid invitation token from an administrator.

## Architecture

The invitation system consists of several key components:

- **InvitationTokenManager**: Manages token generation, validation, and lifecycle
- **InvitationFunction**: Azure Function handling invitation operations
- **EmailService**: Sends invitation and welcome emails via SendGrid
- **AuthenticationFunction**: Modified to require invitation tokens for registration

## System Flow

### 1. Admin Invites User

```
Admin → InvitationFunction (invite-user) → Generate Token → Send Email → User
```

### 2. User Accepts Invitation

```
User → AuthenticationFunction (register) → Validate Token → Create Account → Mark Token Used
```

## API Endpoints

### Invitation Function (`/api/InvitationService`)

#### Invite User

**Operation**: `invite-user`
**Method**: POST
**Request Body**:

```json
{
  "email": "<EMAIL>",
  "firstName": "<PERSON>",
  "lastName": "Doe",
  "applicationId": "MyApp",
  "invitedBy": "<EMAIL>"
}
```

**Response**:

```json
{
  "success": true,
  "message": "Invitation sent successfully",
  "token": "inv_abc123...",
  "verificationCode": "XYZ789",
  "expiresAt": "2025-03-10T14:30:00Z"
}
```

#### Accept Invitation

**Operation**: `accept-invitation`
**Method**: POST
**Request Body**:

```json
{
  "token": "inv_abc123...",
  "verificationCode": "XYZ789",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe"
}
```

**Response**:

```json
{
  "success": true,
  "message": "User account created successfully"
}
```

### Authentication Function (`/api/AuthenticationService`)

#### Register (Invitation Required)

**Operation**: `register`
**Method**: POST
**Request Body**:

```json
{
  "token": "inv_abc123...",
  "verificationCode": "XYZ789",
  "email": "<EMAIL>",
  "password": "SecurePassword123!",
  "firstName": "John",
  "lastName": "Doe"
}
```

## Token Lifecycle

### Generation

- Tokens are cryptographically secure (256-bit)
- Verification codes are 6-digit alphanumeric
- Default expiration: 45 days
- Stored in Azure Blob Storage with metadata

### Validation

- Checks token existence and format
- Verifies verification code match
- Validates expiration date
- Ensures token hasn't been used

### Usage

- Tokens are marked as used after successful registration
- Used tokens cannot be reused
- Expired tokens are automatically invalid

## Security Features

### Rate Limiting

- 5 invitation requests per 15 minutes per IP
- 3 registration attempts per 15 minutes per IP
- Configurable via RateLimitOptions

### Token Security

- Cryptographically secure random generation
- Separate verification codes for additional security
- Time-based expiration
- Single-use tokens

### Email Verification

- Email address must match invitation token
- Prevents token misuse by different users

## Error Handling

### Common Error Responses

#### Invalid Token

```json
{
  "success": false,
  "message": "Invalid or expired invitation token",
  "correlationId": "abc-123-def"
}
```

#### Email Mismatch

```json
{
  "success": false,
  "message": "Email address does not match invitation",
  "correlationId": "abc-123-def"
}
```

#### Rate Limit Exceeded

```json
{
  "success": false,
  "message": "Rate limit exceeded. Please try again later.",
  "errorCode": "RateLimitExceeded",
  "retryAfter": "2025-01-24T15:30:00Z"
}
```

## Power Pages Integration

### Registration Page JavaScript

```javascript
async function acceptInvitation() {
  const urlParams = new URLSearchParams(window.location.search);
  const token = urlParams.get("token");
  const verificationCode = urlParams.get("code");

  if (!token || !verificationCode) {
    showError("Invalid invitation link");
    return;
  }

  const formData = {
    token: token,
    verificationCode: verificationCode,
    email: document.getElementById("email").value,
    password: document.getElementById("password").value,
    firstName: document.getElementById("firstName").value,
    lastName: document.getElementById("lastName").value,
  };

  try {
    const response = await fetch(
      "/api/AuthenticationService?operation=register",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      }
    );

    const result = await response.json();

    if (result.success) {
      window.location.href = "/login?message=Account created successfully";
    } else {
      showError(result.message);
    }
  } catch (error) {
    showError("Registration failed. Please try again.");
  }
}
```

### Invitation URL Format

```
https://yoursite.powerappsportals.com/register?token=inv_abc123...&code=XYZ789
```

## Administrative Functions

### Sending Invitations (Admin Panel)

```javascript
async function sendInvitation(email, firstName, lastName, applicationId) {
  const invitationData = {
    email: email,
    firstName: firstName,
    lastName: lastName,
    applicationId: applicationId,
    invitedBy: getCurrentUserEmail(),
  };

  try {
    const response = await fetch(
      "/api/InvitationService?operation=invite-user",
      {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(invitationData),
      }
    );

    const result = await response.json();

    if (result.success) {
      showSuccess("Invitation sent successfully");
    } else {
      showError(result.message);
    }
  } catch (error) {
    showError("Failed to send invitation");
  }
}
```

## Monitoring and Logging

### Key Log Events

- Invitation token generation
- Email sending attempts
- Token validation attempts
- User registration completion
- Token usage marking

### Correlation IDs

All operations include correlation IDs for request tracking across services.

### Example Log Entries

```
INFO: Invitation token <NAME_EMAIL> [CorrelationId: abc-123-def]
INFO: Invitation email sent <NAME_EMAIL> [CorrelationId: abc-123-def]
INFO: Invitation token validated <NAME_EMAIL> [CorrelationId: xyz-456-ghi]
INFO: User created successfully with ID: 12345 [CorrelationId: xyz-456-ghi]
```

## Troubleshooting

### Token Not Found

- Check token format and case sensitivity
- Verify token hasn't expired
- Ensure token hasn't been used already

### Email Not Sending

- Verify SendGrid configuration
- Check API key permissions
- Validate template IDs
- Review SendGrid activity logs

### Registration Failures

- Verify invitation token is valid
- Check email address matches invitation
- Ensure password meets requirements
- Review rate limiting status

### Common Issues

#### "Invitation token required for registration"

- System now requires invitation tokens for all registrations
- Users must be invited before they can register

#### "Email address does not match invitation"

- User trying to register with different email than invited
- Token security prevents email address changes

#### "Invalid or expired invitation token"

- Token may have expired (45 days default)
- Token may have been used already
- Token format may be incorrect

## Best Practices

### For Administrators

1. Verify email addresses before sending invitations
2. Include clear instructions in invitation emails
3. Monitor invitation usage and expiration
4. Use descriptive application IDs
5. Track invitation metrics

### For Developers

1. Implement proper error handling
2. Use correlation IDs for tracking
3. Validate all input data
4. Implement rate limiting protection
5. Monitor Azure Blob Storage usage

### For Users

1. Use invitation links promptly
2. Check spam folders for invitation emails
3. Use the exact email address that was invited
4. Create strong passwords meeting requirements
5. Contact support if invitation expires

## Migration from Legacy Registration

If migrating from a non-invitation system:

1. **Immediate Migration**: All new registrations require invitations
2. **Granular Control**: Admin can invite specific users
3. **Security Enhancement**: Prevents unauthorized registrations
4. **Email Verification**: Built-in email verification through invitation flow

## Support and Maintenance

### Regular Tasks

- Monitor blob storage for expired tokens
- Review invitation usage patterns
- Update email templates as needed
- Monitor rate limiting effectiveness

### Metrics to Track

- Invitation send rate
- Invitation acceptance rate
- Token expiration rate
- Registration success rate
- Email delivery rate

For technical support or questions about the invitation system, contact your system administrator.
