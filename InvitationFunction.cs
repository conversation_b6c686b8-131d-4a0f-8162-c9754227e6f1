using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;

namespace PasswordHistoryValidator;

public partial class InvitationFunction : BaseFunctionService
{
    private readonly ILogger<InvitationFunction> _logger;
    private readonly InvitationTokenManager _invitationTokenManager;
    private readonly IEmailService _emailService;
    private readonly RateLimitHelper _rateLimitHelper;
    private readonly GraphServiceClient _graphServiceClient;
    private readonly IPasswordHistoryService _passwordHistoryService;
    private readonly IConfiguration _configuration;
    private readonly EntraOptions _entraOptions;

    public InvitationFunction(
        ILogger<InvitationFunction> logger,
        InvitationTokenManager invitationTokenManager,
        IEmailService emailService,
        RateLimitHelper rateLimitHelper,
        GraphServiceClient graphServiceClient,
        IPasswordHistoryService passwordHistoryService,
        IConfiguration configuration,
        IOptions<EntraOptions> entraOptions,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _invitationTokenManager = invitationTokenManager;
        _emailService = emailService;
        _rateLimitHelper = rateLimitHelper;
        _graphServiceClient = graphServiceClient;
        _passwordHistoryService = passwordHistoryService;
        _configuration = configuration;
        _entraOptions = entraOptions.Value;
    }

    [Function("InvitationService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = GenerateCorrelationId();

        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await CreateErrorResponse(req, "Operation parameter required", correlationId);
            }

            return operation.ToLower() switch
            {
                "invite-user" => await HandleInviteUser(req, correlationId, cancellationToken),
                "accept-invitation" => await HandleAcceptInvitation(req, correlationId, cancellationToken),
                "validate-token" => await HandleValidateToken(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Invitation service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleInviteUser(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonSerializer.Deserialize<InvitationRequest>(requestBody, JsonOptions);

            if (data == null)
            {
                return await CreateErrorResponse(req, "Invalid request data", correlationId);
            }

            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(data);
            if (!Validator.TryValidateObject(data, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
            }

            var clientId = BaseFunctionService.GetClientIdentifier(req);
            var rateLimitInfo = _rateLimitHelper.GetRateLimitInfo(clientId, "invite-user");
            if (!rateLimitInfo.IsAllowed)
            {
                return await CreateJsonResponse(req, new
                {
                    success = false,
                    message = "Rate limit exceeded. Please try again later.",
                    errorCode = "RateLimitExceeded",
                    retryAfter = rateLimitInfo.WindowResetTime
                }, HttpStatusCode.TooManyRequests, correlationId);
            }



            // Generate invitation token
            var token = _invitationTokenManager.GenerateInvitationToken();

            // Store invitation token
            var verificationCode = await _invitationTokenManager.StoreInvitationToken(data.ApplicationName, data.Email, token);

            // Send invitation email
            var emailSent = await _emailService.SendUserInvitationEmailAsync(data.Email, token, verificationCode, data.ApplicationName, data.FirstName ?? "", data.LastName ?? "", correlationId);

            if (emailSent)
            {


                return await CreateJsonResponse(req, new
                {
                    success = true,
                    message = "Invitation sent successfully",
                    correlationId = correlationId
                }, HttpStatusCode.OK, correlationId);
            }
            else
            {
                _logger.LogError("Failed to send invitation email to {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
                return await CreateErrorResponse(req, "Failed to send invitation email", correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing invitation request [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Error processing invitation request", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleAcceptInvitation(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonSerializer.Deserialize<InvitationRequest>(requestBody, JsonOptions);

            if (data == null)
            {
                return await CreateErrorResponse(req, "Invalid request data", correlationId);
            }

            // Validate required fields for accept invitation
            if (string.IsNullOrEmpty(data.Token) || string.IsNullOrEmpty(data.VerificationCode) ||
                string.IsNullOrEmpty(data.FirstName) || string.IsNullOrEmpty(data.LastName) ||
                string.IsNullOrEmpty(data.Password))
            {
                return await CreateErrorResponse(req, "Token, verification code, first name, last name, and password are required for accepting invitation", correlationId);
            }

            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(data);
            if (!Validator.TryValidateObject(data, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
            }

            var clientId = BaseFunctionService.GetClientIdentifier(req);
            var rateLimitInfo = _rateLimitHelper.GetRateLimitInfo(clientId, "accept-invitation");
            if (!rateLimitInfo.IsAllowed)
            {
                return await CreateJsonResponse(req, new
                {
                    success = false,
                    message = "Rate limit exceeded. Please try again later.",
                    errorCode = "RateLimitExceeded",
                    retryAfter = rateLimitInfo.WindowResetTime
                }, HttpStatusCode.TooManyRequests, correlationId);
            }



            // Validate invitation token
            var (isValid, tokenData, errorMessage) = await _invitationTokenManager.ValidateInvitationToken(data.Token, data.VerificationCode);

            if (!isValid)
            {
                _logger.LogWarning("Invalid invitation token for {Email}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                    data.Email, errorMessage, correlationId);
                return await CreateErrorResponse(req, errorMessage, correlationId);
            }

            if (tokenData == null)
            {
                return await CreateErrorResponse(req, "Invalid invitation token data", correlationId);
            }

            // Verify email matches token
            if (!string.Equals(tokenData.Email, data.Email, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogWarning("Email mismatch in invitation token for {Email} [CorrelationId: {CorrelationId}]",
                    data.Email, correlationId);
                return await CreateErrorResponse(req, "Email address does not match invitation", correlationId);
            }

            try
            {
                // Check for existing users
                var existingUsers = await _graphServiceClient.Users
                    .GetAsync(requestConfiguration =>
                    {
                        requestConfiguration.QueryParameters.Filter =
                            $"mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')";
                        requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName" };
                    }, cancellationToken);

                if (existingUsers?.Value?.Any() == true)
                {
                    return await CreateErrorResponse(req, "User already exists", correlationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "User check error [CorrelationId: {CorrelationId}]", correlationId);
                return await CreateErrorResponse(req, "Error checking user existence", correlationId);
            }

            try
            {
                // Validate password against history
                var passwordValidation = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
                    tokenData.ApplicationId, data.Email, data.Password, cancellationToken);

                if (!passwordValidation.IsSuccess)
                {
                    return await CreateErrorResponse(req, passwordValidation.ErrorMessage, correlationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Password validation error [CorrelationId: {CorrelationId}]", correlationId);
                return await CreateErrorResponse(req, "Password validation error", correlationId);
            }

            try
            {
                // Create new user
                var upn = $"{Guid.NewGuid()}@{_entraOptions.DefaultDomain}";
                var applicationName = tokenData.ApplicationId;
                var displayNameWithContext = $"{data.FirstName} {data.LastName} ({applicationName})";
                var applicationContext = applicationName;

                var newUser = new User
                {
                    DisplayName = displayNameWithContext,
                    GivenName = data.FirstName,
                    Surname = data.LastName,
                    Mail = data.Email,
                    UserPrincipalName = upn,
                    Department = applicationContext,
                    Identities = new List<ObjectIdentity>
                    {
                        new ObjectIdentity
                        {
                            SignInType = "emailAddress",
                            Issuer = _entraOptions.DefaultDomain,
                            IssuerAssignedId = data.Email
                        }
                    },
                    PasswordProfile = new PasswordProfile
                    {
                        Password = data.Password,
                        ForceChangePasswordNextSignIn = false
                    },
                    AccountEnabled = true
                };

                var createdUser = await _graphServiceClient.Users.PostAsync(newUser, cancellationToken: cancellationToken);

                if (createdUser?.Id == null)
                {
                    return await CreateErrorResponse(req, "Failed to create user", correlationId);
                }



                // Update password history
                try
                {
                    await _passwordHistoryService.UpdatePasswordHistoryAsync(tokenData.ApplicationId, data.Email, data.Password, cancellationToken);
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Password history update error for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
                    // Don't fail the registration for password history errors
                }

                // Mark invitation token as used
                var tokenMarked = await _invitationTokenManager.MarkTokenAsUsed(data.Token);
                if (!tokenMarked)
                {
                    _logger.LogWarning("Failed to mark invitation token as used for {Email} [CorrelationId: {CorrelationId}]",
                        data.Email, correlationId);
                }

                // Send account created notification
                var notificationSent = await _emailService.SendAccountCreatedNotificationAsync(data.Email, data.FirstName, tokenData.ApplicationId, correlationId);
                if (!notificationSent)
                {
                    _logger.LogWarning("Failed to send account created notification to {Email} [CorrelationId: {CorrelationId}]",
                        data.Email, correlationId);
                }

                return await CreateJsonResponse(req, new
                {
                    success = true,
                    message = "Account created successfully",
                    userId = createdUser.Id,
                    correlationId = correlationId
                }, HttpStatusCode.OK, correlationId);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "User creation error [CorrelationId: {CorrelationId}]: {ErrorMessage}", correlationId, ex.Message);

                var userCreationErrorMessage = ex.Message.Contains("already exists") || ex.Message.Contains("duplicate")
                    ? "A user with this email address already exists. Please use a different email or try logging in."
                    : "Error creating user account";

                return await CreateErrorResponse(req, userCreationErrorMessage, correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error processing invitation acceptance [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Error processing invitation acceptance", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleValidateToken(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
{
    try
    {
        var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
        var data = JsonSerializer.Deserialize<TokenValidationRequest>(requestBody, JsonOptions);

        if (data == null || string.IsNullOrEmpty(data.Token))
        {
            return await CreateErrorResponse(req, "Token is required", correlationId);
        }

        var clientId = BaseFunctionService.GetClientIdentifier(req);
        var rateLimitInfo = _rateLimitHelper.GetRateLimitInfo(clientId, "validate-token");
        if (!rateLimitInfo.IsAllowed)
        {
            return await CreateJsonResponse(req, new
            {
                success = false,
                message = "Rate limit exceeded. Please try again later.",
                errorCode = "RateLimitExceeded",
                retryAfter = rateLimitInfo.WindowResetTime
            }, HttpStatusCode.TooManyRequests, correlationId);
        }

        // Use InvitationTokenManager to validate token only (for page access)
        var (isValid, tokenData, errorMessage) = await _invitationTokenManager.ValidateInvitationTokenOnly(data.Token);

        if (!isValid || tokenData == null)
        {
            _logger.LogWarning("Token validation failed: {ErrorMessage} [CorrelationId: {CorrelationId}]", 
                errorMessage, correlationId);
            return await CreateJsonResponse(req, new
            {
                success = false,
                message = errorMessage ?? "Invalid or expired invitation token"
            }, HttpStatusCode.Unauthorized, correlationId);
        }

        // Token is valid - grant page access
        _logger.LogInformation("Token validation successful for {Email} [CorrelationId: {CorrelationId}]", 
            tokenData.Email, correlationId);

        return await CreateJsonResponse(req, new
        {
            success = true,
            message = "Token is valid",
            email = tokenData.Email,
            applicationId = tokenData.ApplicationId,
            expiresUtc = tokenData.ExpiresUtc
        }, HttpStatusCode.OK, correlationId);
    }
    catch (Exception ex)
    {
        _logger.LogError(ex, "Error processing token validation [CorrelationId: {CorrelationId}]", correlationId);
        return await CreateErrorResponse(req, "Error processing token validation", correlationId);
    }
}

}
