using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using Azure.Identity;
using System.Collections.Generic;
using System.Linq;

namespace PasswordHistoryValidator;

public class AuthenticationFunction : BaseFunctionService
{
    private readonly ILogger<AuthenticationFunction> _logger;
    private readonly IPasswordHistoryService _passwordHistoryService;
    private readonly RateLimitHelper _rateLimitHelper;
    private readonly GraphServiceClient _graphServiceClient;
    private readonly IConfiguration _configuration;
    private readonly EntraOptions _entraOptions;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly InvitationTokenManager _invitationTokenManager;

    public AuthenticationFunction(
        ILogger<AuthenticationFunction> logger,
        IPasswordHistoryService passwordHistoryService,
        RateLimitHelper rateLimitHelper,
        GraphServiceClient graphServiceClient,
        IConfiguration configuration,
        IOptions<EntraOptions> entraOptions,
        IHttpClientFactory httpClientFactory,
        InvitationTokenManager invitationTokenManager,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _passwordHistoryService = passwordHistoryService;
        _rateLimitHelper = rateLimitHelper;
        _graphServiceClient = graphServiceClient;
        _configuration = configuration;
        _entraOptions = entraOptions.Value;
        _httpClientFactory = httpClientFactory;
        _invitationTokenManager = invitationTokenManager;
    }


    [Function("AuthenticationService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = GenerateCorrelationId();

        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await CreateErrorResponse(req, "Operation parameter required", correlationId);
            }

            return operation.ToLower() switch
            {
                "register" => await HandleUserRegistration(req, correlationId, cancellationToken),
                "login" => await HandleUserLogin(req, correlationId, cancellationToken),
                "validate-credentials" => await HandleCredentialValidation(req, correlationId, cancellationToken),
                "list-duplicates" => await HandleListDuplicateUsers(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Auth service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleUserLogin(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            if (string.IsNullOrWhiteSpace(requestBody))
            {
                return await CreateErrorResponse(req, "Request body required", correlationId);
            }

            var data = JsonSerializer.Deserialize<AuthRequest>(requestBody, JsonOptions);
            if (data == null || string.IsNullOrWhiteSpace(data.Email) || string.IsNullOrWhiteSpace(data.Password))
            {
                return await CreateErrorResponse(req, "Email and password required", correlationId);
            }

            try
            {
                var applicationName = data.ApplicationName ?? "Default Application";
                var users = await _graphServiceClient.Users
                    .GetAsync(requestConfiguration =>
                    {
                        requestConfiguration.QueryParameters.Filter =
                            $"(mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')) and startswith(department, '{applicationName}')";
                        requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "identities", "mail", "displayName", "department" };
                        requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
                    }, cancellationToken);

                if (users?.Value == null || users.Value.Count == 0)
                {
                    _logger.LogWarning("Login failed for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
                    return await CreateErrorResponse(req, "Invalid email or password", correlationId);
                }

                User user;
                if (users.Value.Count > 1)
                {
                    _logger.LogWarning("Multiple users found for {Email}, selecting most recent [CorrelationId: {CorrelationId}]", data.Email, correlationId);
                    user = users.Value.OrderBy(u => u.Id).Last();
                }
                else
                {
                    user = users.Value.First();
                }

                var usernameForAuth = user.UserPrincipalName ?? data.Email;
                var isValidCredentials = await ValidateUserCredentials(usernameForAuth, data.Password, data.ApplicationName ?? "Default Application", data.Email, cancellationToken);

                if (!isValidCredentials && !string.Equals(usernameForAuth, data.Email, StringComparison.OrdinalIgnoreCase))
                {
                    isValidCredentials = await ValidateUserCredentials(data.Email, data.Password, data.ApplicationName ?? "Default Application", data.Email, cancellationToken);
                }

                if (!isValidCredentials)
                {
                    return await CreateErrorResponse(req, "Invalid email or password", correlationId);
                }

                return await CreateJsonResponse(req, new
                {
                    success = true,
                    message = "Login successful",
                    userId = user.Id,
                    email = user.Mail,
                    displayName = user.DisplayName
                }, HttpStatusCode.OK, correlationId);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Auth error [CorrelationId: {CorrelationId}]", correlationId);
                return await CreateErrorResponse(req, "Authentication failed", correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Login error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Login failed", correlationId);
        }
    }

    // Validate credentials using password history (ROPC fallback for external domains)
    private async Task<bool> ValidateUserCredentials(string username, string password, string applicationName, string email, CancellationToken cancellationToken)
    {
        try
        {
            var historyResult = await _passwordHistoryService.GetPasswordHistoryAsync(
                applicationName ?? "Default Application",
                email,
                cancellationToken);

            if (historyResult.IsSuccess && historyResult.Value != null && historyResult.Value.Count > 0)
            {
                var mostRecentHash = historyResult.Value.First();
                if (BCrypt.Net.BCrypt.Verify(password, mostRecentHash))
                {
                    return true;
                }
            }

            return await TryROPCValidation(username, password, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Credential validation error for {Email}", email);
            return false;
        }
    }

    // Fallback ROPC validation attempt
    private async Task<bool> TryROPCValidation(string email, string password, CancellationToken cancellationToken)
    {
        try
        {
            var tokenEndpoint = $"https://login.microsoftonline.com/{_entraOptions.TenantId}/oauth2/v2.0/token";

            var formData = new List<KeyValuePair<string, string>>
            {
                new("grant_type", "password"),
                new("client_id", _entraOptions.ClientId),
                new("client_secret", _entraOptions.ClientSecret),
                new("scope", "https://graph.microsoft.com/.default"),
                new("username", email),
                new("password", password)
            };

            using var httpClient = _httpClientFactory.CreateClient();
            using var content = new FormUrlEncodedContent(formData);

            var response = await httpClient.PostAsync(tokenEndpoint, content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private async Task<HttpResponseData> HandleCredentialValidation(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            if (string.IsNullOrWhiteSpace(requestBody))
            {
                return await CreateErrorResponse(req, "Request body required", correlationId);
            }

            var data = JsonSerializer.Deserialize<AuthRequest>(requestBody, JsonOptions);
            if (data == null || string.IsNullOrWhiteSpace(data.Email) || string.IsNullOrWhiteSpace(data.Password))
            {
                return await CreateErrorResponse(req, "Email and password required", correlationId);
            }

            var applicationName = data.ApplicationName ?? "Default Application";
            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter =
                        $"(mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')) and startswith(department, '{applicationName}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "mail", "displayName", "department" };
                }, cancellationToken);

            if (users?.Value == null || users.Value.Count == 0)
            {
                return await CreateErrorResponse(req, "Invalid email or password", correlationId);
            }

            var user = users.Value.First();
            var usernameForAuth = user.UserPrincipalName ?? data.Email;
            var isValidCredentials = await ValidateUserCredentials(usernameForAuth, data.Password, data.ApplicationName ?? "Default Application", data.Email, cancellationToken);

            if (isValidCredentials)
            {
                return await CreateJsonResponse(req, new
                {
                    success = true,
                    message = "Credentials validated successfully",
                    email = user.Mail,
                    displayName = user.DisplayName
                }, HttpStatusCode.OK, correlationId);
            }
            else
            {
                return await CreateErrorResponse(req, "Invalid email or password", correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Credential validation error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Validation failed", correlationId);
        }
    }



    private async Task<HttpResponseData> HandleUserRegistration(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();

            // Deserialize as InvitationRequest (required for registration)
            InvitationRequest? invitationData = null;

            try
            {
                invitationData = JsonSerializer.Deserialize<InvitationRequest>(requestBody, JsonOptions);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Failed to parse invitation request [CorrelationId: {CorrelationId}]", correlationId);
                return await CreateErrorResponse(req, "Invalid request format", correlationId);
            }

            // Validate we have valid invitation data
            if (invitationData == null)
            {
                return await CreateErrorResponse(req, "Invalid request data", correlationId);
            }

            // Require invitation verification code for all registrations
            if (string.IsNullOrEmpty(invitationData.VerificationCode))
            {
                return await CreateErrorResponse(req, "Invitation code required for registration", correlationId);
            }

            // Validate the invitation request data
            var validationResults = new List<ValidationResult>();
            var validationContext = new ValidationContext(invitationData);
            if (!Validator.TryValidateObject(invitationData, validationContext, validationResults, true))
            {
                var errors = string.Join(", ", validationResults.Select(r => r.ErrorMessage));
                return await CreateErrorResponse(req, $"Validation failed: {errors}", correlationId);
            }

            // Validate invitation code
            var (isValid, tokenData, errorMessage) = string.IsNullOrEmpty(invitationData.Token)
                ? await _invitationTokenManager.ValidateInvitationByCode(invitationData.VerificationCode)
                : await _invitationTokenManager.ValidateInvitationToken(invitationData.Token, invitationData.VerificationCode);

            if (!isValid)
            {
                _logger.LogWarning("Invalid invitation token for {Email}: {ErrorMessage} [CorrelationId: {CorrelationId}]",
                    invitationData.Email, errorMessage, correlationId);
                return await CreateErrorResponse(req, errorMessage, correlationId);
            }

            if (tokenData == null)
            {
                return await CreateErrorResponse(req, "Invalid invitation token data", correlationId);
            }

            // Verify email matches token
            if (!string.Equals(tokenData.Email, invitationData.Email, StringComparison.OrdinalIgnoreCase))
            {
                _logger.LogWarning("Email mismatch in invitation token for {Email} [CorrelationId: {CorrelationId}]",
                    invitationData.Email, correlationId);
                return await CreateErrorResponse(req, "Email address does not match invitation", correlationId);
            }

            // Create unified data object for the rest of the method
            var data = new AuthRequest
            {
                Email = invitationData.Email,
                Password = invitationData.Password ?? string.Empty,
                FirstName = invitationData.FirstName,
                LastName = invitationData.LastName,
                ApplicationName = tokenData.ApplicationId
            };



            var clientId = BaseFunctionService.GetClientIdentifier(req);
            var rateLimitInfo = _rateLimitHelper.GetRateLimitInfo(clientId, "register");
            if (!rateLimitInfo.IsAllowed)
            {
                return await CreateJsonResponse(req, new
                {
                    success = false,
                    message = "Rate limit exceeded. Please try again later.",
                    errorCode = "RateLimitExceeded",
                    retryAfter = rateLimitInfo.WindowResetTime
                }, HttpStatusCode.TooManyRequests, correlationId);
            }

            try
            {
                var existingUsers = await _graphServiceClient.Users
                    .GetAsync(requestConfiguration =>
                    {
                        requestConfiguration.QueryParameters.Filter =
                            $"mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')";
                        requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName" };
                    }, cancellationToken);

                if (existingUsers?.Value?.Any() == true)
                {
                    return await CreateErrorResponse(req, "User already exists", correlationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "User check error [CorrelationId: {CorrelationId}]", correlationId);
                return await CreateErrorResponse(req, "Error checking user existence", correlationId);
            }

            try
            {
                var passwordValidation = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
                    data.ApplicationName, data.Email, data.Password, cancellationToken);

                if (!passwordValidation.IsSuccess)
                {
                    return await CreateErrorResponse(req, passwordValidation.ErrorMessage, correlationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Password validation error [CorrelationId: {CorrelationId}]", correlationId);
                return await CreateErrorResponse(req, "Password validation error", correlationId);
            }

            // Prevent duplicate users in same application
            try
            {
                var applicationName = data.ApplicationName ?? "Default Application";
                var existingUsers = await _graphServiceClient.Users
                    .GetAsync(requestConfiguration =>
                    {
                        requestConfiguration.QueryParameters.Filter =
                            $"(mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')) and startswith(department, '{applicationName}')";
                        requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName", "department" };
                    }, cancellationToken);

                if (existingUsers?.Value != null && existingUsers.Value.Count > 0)
                {
                    _logger.LogWarning("Registration blocked - email {Email} already exists [CorrelationId: {CorrelationId}]", data.Email, correlationId);

                    foreach (var existingUser in existingUsers.Value)
                    {
                        _logger.LogInformation("Existing user in app: ID={UserId}, Email={Email}, UPN={UPN}, DisplayName={DisplayName}, Department={Department}",
                            existingUser.Id, existingUser.Mail, existingUser.UserPrincipalName, existingUser.DisplayName, existingUser.Department);
                    }

                    return await CreateErrorResponse(req, $"A user with this email address already exists in application '{applicationName}'. Please use a different email or try logging in.", correlationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error checking for existing users during registration [CorrelationId: {CorrelationId}]", correlationId);

            }

            try
            {
                var upn = $"{Guid.NewGuid()}@{_entraOptions.DefaultDomain}";
                var applicationName = data.ApplicationName ?? "Default Application";
                var displayNameWithContext = $"{data.FirstName} {data.LastName} ({applicationName})";
                var applicationContext = applicationName;
                if (!string.IsNullOrWhiteSpace(data.Department))
                {
                    applicationContext += $" - {data.Department}";
                }

                var newUser = new User
                {
                    DisplayName = displayNameWithContext,
                    GivenName = data.FirstName,
                    Surname = data.LastName,
                    Mail = data.Email,
                    UserPrincipalName = upn,
                    Department = applicationContext,
                    Identities = new List<ObjectIdentity>
                    {
                        new ObjectIdentity
                        {
                            SignInType = "emailAddress",
                            Issuer = _entraOptions.DefaultDomain,
                            IssuerAssignedId = data.Email
                        }
                    },
                    PasswordProfile = new PasswordProfile
                    {
                        Password = data.Password,
                        ForceChangePasswordNextSignIn = false
                    },
                    AccountEnabled = true
                };

                var createdUser = await _graphServiceClient.Users.PostAsync(newUser, cancellationToken: cancellationToken);

                if (createdUser?.Id == null)
                {
                    return await CreateErrorResponse(req, "Failed to create user", correlationId);
                }



                // Mark invitation token as used
                var tokenMarked = await _invitationTokenManager.MarkTokenAsUsed(tokenData.Token);
                if (tokenMarked)
                {

                }
                else
                {
                    _logger.LogWarning("Failed to mark invitation token as used for {Email} [CorrelationId: {CorrelationId}]",
                        data.Email, correlationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "User creation error [CorrelationId: {CorrelationId}]: {ErrorMessage}", correlationId, ex.Message);


                var userCreationErrorMessage = ex.Message.Contains("already exists") || ex.Message.Contains("duplicate")
                    ? "A user with this email address already exists. Please use a different email or try logging in."
                    : ex.Message.Contains("domain") || ex.Message.Contains("tenant")
                    ? "Domain configuration error. Please contact support."
                    : ex.Message.Contains("password") || ex.Message.Contains("Password")
                    ? "Password does not meet security requirements. Please ensure it has uppercase, lowercase, numbers, and special characters."
                    : $"User creation failed: {ex.Message}";

                return await CreateErrorResponse(req, userCreationErrorMessage, correlationId);
            }

            try
            {
                var historyUpdate = await _passwordHistoryService.UpdatePasswordHistoryAsync(
                    data.ApplicationName ?? "Default Application", data.Email, data.Password, cancellationToken);

                if (!historyUpdate.IsSuccess)
                {
                    _logger.LogError("Password history update failed for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Password history error for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
            }

            return await CreateJsonResponse(req, new { success = true, message = "User account created successfully" }, HttpStatusCode.OK, correlationId);

        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Registration error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Registration failed", correlationId);
        }
    }

    private async Task<Dictionary<string, object>?> GetUserApplicationDataAsync(string userId, CancellationToken cancellationToken)
    {
        try
        {
            var user = await _graphServiceClient.Users[userId]
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Select = new[]
                    {
                        "id", "mail", "displayName",
                        "extension_ApplicationId", "extension_ApplicationName",
                        "extension_Department", "extension_UserRole",
                        "extension_RegistrationDate", "extension_RegistrationSource",
                        "extension_PasswordPolicyLevel", "extension_LastPasswordHistoryCheck"
                    };
                }, cancellationToken);

            if (user?.AdditionalData != null)
            {
                var applicationData = new Dictionary<string, object>();

                foreach (var kvp in user.AdditionalData)
                {
                    if (kvp.Key.StartsWith("extension_") && kvp.Value != null)
                    {
                        applicationData[kvp.Key] = kvp.Value;
                    }
                }

                return applicationData.Count > 0 ? applicationData : null;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get user data for {UserId}", userId);
            return null;
        }
    }



    private async Task<HttpResponseData> HandleListDuplicateUsers(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonSerializer.Deserialize<AuthRequest>(requestBody, JsonOptions);

            if (data == null || string.IsNullOrWhiteSpace(data.Email))
            {
                return await CreateErrorResponse(req, "Email is required", correlationId);
            }


            var applicationName = data.ApplicationName ?? "Default Application";
            var appUsers = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter =
                        $"(mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')) and startswith(department, '{applicationName}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "mail", "displayName", "department", "createdDateTime" };
                    requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
                }, cancellationToken);


            var allUsers = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter =
                        $"mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "mail", "displayName", "department", "createdDateTime" };
                    requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
                }, cancellationToken);

            var appUserList = appUsers?.Value?.Select(u => new
            {
                id = u.Id,
                email = u.Mail,
                userPrincipalName = u.UserPrincipalName,
                displayName = u.DisplayName,
                department = u.Department,
                createdDateTime = u.CreatedDateTime,
                scope = "current-application"
            }).ToList();

            var allUserList = allUsers?.Value?.Select(u => new
            {
                id = u.Id,
                email = u.Mail,
                userPrincipalName = u.UserPrincipalName,
                displayName = u.DisplayName,
                department = u.Department,
                createdDateTime = u.CreatedDateTime,
                scope = u.Department?.StartsWith(applicationName) == true ? "current-application" : "other-application"
            }).ToList();

            var appUserCount = appUserList?.Count ?? 0;
            var totalUserCount = allUserList?.Count ?? 0;



            var responseAppUsers = appUserList != null ? appUserList.Cast<object>().ToList() : new List<object>();
            var responseAllUsers = allUserList != null ? allUserList.Cast<object>().ToList() : new List<object>();

            return await CreateJsonResponse(req, new
            {
                email = data.Email,
                applicationName = applicationName,
                currentApplication = new
                {
                    userCount = appUserCount,
                    users = responseAppUsers,
                    hasDuplicates = appUserCount > 1
                },
                allApplications = new
                {
                    userCount = totalUserCount,
                    users = responseAllUsers,
                    hasMultipleApplications = totalUserCount > appUserCount
                }
            }, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error listing duplicate users [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Failed to list users", correlationId);
        }
    }

    #region User Resolution Methods (Consolidated from UserResolution service)

    /// <summary>
    /// Resolves email address to Graph User ID with optional application context
    /// </summary>
    private async Task<string> ResolveEmailToUserId(string email, string? applicationId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Resolving email {Email} to User ID for application {ApplicationId}", email, applicationId ?? "any");

            var emailFilter = $"mail eq '{email}' or userPrincipalName eq '{email}' or proxyAddresses/any(c:c eq 'SMTP:{email}')";
            var filter = emailFilter;

            if (!string.IsNullOrEmpty(applicationId))
            {
                filter = $"({emailFilter}) and startswith(department, '{applicationId}')";
            }

            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter = filter;
                    requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName", "department" };
                }, cancellationToken);

            var user = users?.Value?.FirstOrDefault();
            return user?.Id ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving email {Email} to User ID: {Message}", email, ex.Message);
            return string.Empty;
        }
    }

    /// <summary>
    /// Gets user details by email with application context
    /// </summary>
    private async Task<User?> GetUserByEmail(string email, string? applicationName = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting user details for email {Email} in application '{ApplicationName}'", email, applicationName ?? "any");

            var emailFilter = $"mail eq '{email}' or userPrincipalName eq '{email}' or proxyAddresses/any(c:c eq 'SMTP:{email}')";
            var filter = emailFilter;

            if (!string.IsNullOrEmpty(applicationName))
            {
                filter = $"({emailFilter}) and startswith(department, '{applicationName}')";
            }

            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter = filter;
                    requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName", "department" };
                    requestConfiguration.QueryParameters.Top = 1;
                }, cancellationToken);

            return users?.Value?.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user details for email {Email} in application '{ApplicationName}': {Message}",
                email, applicationName ?? "any", ex.Message);
            return null;
        }
    }

    /// <summary>
    /// Checks if user exists in specific application context
    /// </summary>
    private async Task<bool> CheckUserExistsInApplication(string email, string applicationName, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Checking if user exists for email {Email} in application '{ApplicationName}'", email, applicationName);

            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter =
                        $"(mail eq '{email}' or userPrincipalName eq '{email}' or proxyAddresses/any(c:c eq 'SMTP:{email}')) and startswith(department, '{applicationName}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "department" };
                    requestConfiguration.QueryParameters.Top = 1;
                }, cancellationToken);

            var exists = users?.Value?.Count > 0;
            _logger.LogDebug("User existence check for {Email} in application '{ApplicationName}': {Exists}", email, applicationName, exists);
            return exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking user existence for {Email} in application '{ApplicationName}'", email, applicationName);
            return false;
        }
    }

    #endregion
}
