using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Options;
using Microsoft.Extensions.Configuration;
using Microsoft.Graph;
using Microsoft.Graph.Models;
using System.Net;
using System.Text.Json;
using System.ComponentModel.DataAnnotations;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using Azure.Identity;
using System.Collections.Generic;
using System.Linq;

namespace PasswordHistoryValidator;

public class AuthenticationFunction : BaseFunctionService
{
    private readonly ILogger<AuthenticationFunction> _logger;
    private readonly IPasswordHistoryService _passwordHistoryService;
    private readonly RateLimitHelper _rateLimitHelper;
    private readonly GraphServiceClient _graphServiceClient;
    private readonly IConfiguration _configuration;
    private readonly EntraOptions _entraOptions;
    private readonly IHttpClientFactory _httpClientFactory;
    private readonly InvitationTokenManager _invitationTokenManager;

    public AuthenticationFunction(
        ILogger<AuthenticationFunction> logger,
        IPasswordHistoryService passwordHistoryService,
        RateLimitHelper rateLimitHelper,
        GraphServiceClient graphServiceClient,
        IConfiguration configuration,
        IOptions<EntraOptions> entraOptions,
        IHttpClientFactory httpClientFactory,
        InvitationTokenManager invitationTokenManager,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _passwordHistoryService = passwordHistoryService;
        _rateLimitHelper = rateLimitHelper;
        _graphServiceClient = graphServiceClient;
        _configuration = configuration;
        _entraOptions = entraOptions.Value;
        _httpClientFactory = httpClientFactory;
        _invitationTokenManager = invitationTokenManager;
    }


    [Function("AuthenticationService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", "options")] HttpRequestData req,
        CancellationToken cancellationToken)
    {
        var correlationId = GenerateCorrelationId();

        if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
        {
            return CreateCorsResponse(req);
        }

        try
        {
            var operation = req.Query["operation"];
            if (string.IsNullOrEmpty(operation))
            {
                return await CreateErrorResponse(req, "Operation parameter required", correlationId);
            }

            return operation.ToLower() switch
            {
                "login" => await HandleUserLogin(req, correlationId, cancellationToken),
                "validate-credentials" => await HandleCredentialValidation(req, correlationId, cancellationToken),
                "list-duplicates" => await HandleListDuplicateUsers(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Auth service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleUserLogin(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            if (string.IsNullOrWhiteSpace(requestBody))
            {
                return await CreateErrorResponse(req, "Request body required", correlationId);
            }

            var data = JsonSerializer.Deserialize<AuthRequest>(requestBody, JsonOptions);
            if (data == null || string.IsNullOrWhiteSpace(data.Email) || string.IsNullOrWhiteSpace(data.Password))
            {
                return await CreateErrorResponse(req, "Email and password required", correlationId);
            }

            try
            {
                var applicationName = data.ApplicationName ?? "Default Application";
                var users = await _graphServiceClient.Users
                    .GetAsync(requestConfiguration =>
                    {
                        requestConfiguration.QueryParameters.Filter =
                            $"(mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')) and startswith(department, '{applicationName}')";
                        requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "identities", "mail", "displayName", "department" };
                        requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
                    }, cancellationToken);

                if (users?.Value == null || users.Value.Count == 0)
                {
                    _logger.LogWarning("Login failed for {Email} [CorrelationId: {CorrelationId}]", data.Email, correlationId);
                    return await CreateErrorResponse(req, "Invalid email or password", correlationId);
                }

                User user;
                if (users.Value.Count > 1)
                {
                    _logger.LogWarning("Multiple users found for {Email}, selecting most recent [CorrelationId: {CorrelationId}]", data.Email, correlationId);
                    user = users.Value.OrderBy(u => u.Id).Last();
                }
                else
                {
                    user = users.Value.First();
                }

                var usernameForAuth = user.UserPrincipalName ?? data.Email;
                var isValidCredentials = await ValidateUserCredentials(usernameForAuth, data.Password, data.ApplicationName ?? "Default Application", data.Email, cancellationToken);

                if (!isValidCredentials && !string.Equals(usernameForAuth, data.Email, StringComparison.OrdinalIgnoreCase))
                {
                    isValidCredentials = await ValidateUserCredentials(data.Email, data.Password, data.ApplicationName ?? "Default Application", data.Email, cancellationToken);
                }

                if (!isValidCredentials)
                {
                    return await CreateErrorResponse(req, "Invalid email or password", correlationId);
                }

                return await CreateJsonResponse(req, new
                {
                    success = true,
                    message = "Login successful",
                    userId = user.Id,
                    email = user.Mail,
                    displayName = user.DisplayName
                }, HttpStatusCode.OK, correlationId);

            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Auth error [CorrelationId: {CorrelationId}]", correlationId);
                return await CreateErrorResponse(req, "Authentication failed", correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Login error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Login failed", correlationId);
        }
    }

    // Validate credentials using password history (ROPC fallback for external domains)
    private async Task<bool> ValidateUserCredentials(string username, string password, string applicationName, string email, CancellationToken cancellationToken)
    {
        try
        {
            var historyResult = await _passwordHistoryService.GetPasswordHistoryAsync(
                applicationName ?? "Default Application",
                email,
                cancellationToken);

            if (historyResult.IsSuccess && historyResult.Value != null && historyResult.Value.Count > 0)
            {
                var mostRecentHash = historyResult.Value.First();
                if (BCrypt.Net.BCrypt.Verify(password, mostRecentHash))
                {
                    return true;
                }
            }

            return await TryROPCValidation(username, password, cancellationToken);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Credential validation error for {Email}", email);
            return false;
        }
    }

    // Fallback ROPC validation attempt
    private async Task<bool> TryROPCValidation(string email, string password, CancellationToken cancellationToken)
    {
        try
        {
            var tokenEndpoint = $"https://login.microsoftonline.com/{_entraOptions.TenantId}/oauth2/v2.0/token";

            var formData = new List<KeyValuePair<string, string>>
            {
                new("grant_type", "password"),
                new("client_id", _entraOptions.ClientId),
                new("client_secret", _entraOptions.ClientSecret),
                new("scope", "https://graph.microsoft.com/.default"),
                new("username", email),
                new("password", password)
            };

            using var httpClient = _httpClientFactory.CreateClient();
            using var content = new FormUrlEncodedContent(formData);

            var response = await httpClient.PostAsync(tokenEndpoint, content, cancellationToken);
            return response.IsSuccessStatusCode;
        }
        catch
        {
            return false;
        }
    }

    private async Task<HttpResponseData> HandleCredentialValidation(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            if (string.IsNullOrWhiteSpace(requestBody))
            {
                return await CreateErrorResponse(req, "Request body required", correlationId);
            }

            var data = JsonSerializer.Deserialize<AuthRequest>(requestBody, JsonOptions);
            if (data == null || string.IsNullOrWhiteSpace(data.Email) || string.IsNullOrWhiteSpace(data.Password))
            {
                return await CreateErrorResponse(req, "Email and password required", correlationId);
            }

            var applicationName = data.ApplicationName ?? "Default Application";
            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter =
                        $"(mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')) and startswith(department, '{applicationName}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "mail", "displayName", "department" };
                }, cancellationToken);

            if (users?.Value == null || users.Value.Count == 0)
            {
                return await CreateErrorResponse(req, "Invalid email or password", correlationId);
            }

            var user = users.Value.First();
            var usernameForAuth = user.UserPrincipalName ?? data.Email;
            var isValidCredentials = await ValidateUserCredentials(usernameForAuth, data.Password, data.ApplicationName ?? "Default Application", data.Email, cancellationToken);

            if (isValidCredentials)
            {
                return await CreateJsonResponse(req, new
                {
                    success = true,
                    message = "Credentials validated successfully",
                    email = user.Mail,
                    displayName = user.DisplayName
                }, HttpStatusCode.OK, correlationId);
            }
            else
            {
                return await CreateErrorResponse(req, "Invalid email or password", correlationId);
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Credential validation error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Validation failed", correlationId);
        }
    }

    private async Task<Dictionary<string, object>?> GetUserApplicationDataAsync(string userId, CancellationToken cancellationToken)
    {
        try
        {
            var user = await _graphServiceClient.Users[userId]
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Select = new[]
                    {
                        "id", "mail", "displayName",
                        "extension_ApplicationId", "extension_ApplicationName",
                        "extension_Department", "extension_UserRole",
                        "extension_RegistrationDate", "extension_RegistrationSource",
                        "extension_PasswordPolicyLevel", "extension_LastPasswordHistoryCheck"
                    };
                }, cancellationToken);

            if (user?.AdditionalData != null)
            {
                var applicationData = new Dictionary<string, object>();

                foreach (var kvp in user.AdditionalData)
                {
                    if (kvp.Key.StartsWith("extension_") && kvp.Value != null)
                    {
                        applicationData[kvp.Key] = kvp.Value;
                    }
                }

                return applicationData.Count > 0 ? applicationData : null;
            }

            return null;
        }
        catch (Exception ex)
        {
            _logger.LogWarning(ex, "Failed to get user data for {UserId}", userId);
            return null;
        }
    }



    private async Task<HttpResponseData> HandleListDuplicateUsers(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var requestBody = await new StreamReader(req.Body).ReadToEndAsync();
            var data = JsonSerializer.Deserialize<AuthRequest>(requestBody, JsonOptions);

            if (data == null || string.IsNullOrWhiteSpace(data.Email))
            {
                return await CreateErrorResponse(req, "Email is required", correlationId);
            }


            var applicationName = data.ApplicationName ?? "Default Application";
            var appUsers = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter =
                        $"(mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')) and startswith(department, '{applicationName}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "mail", "displayName", "department", "createdDateTime" };
                    requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
                }, cancellationToken);


            var allUsers = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter =
                        $"mail eq '{data.Email}' or userPrincipalName eq '{data.Email}' or proxyAddresses/any(c:c eq 'SMTP:{data.Email}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id", "userPrincipalName", "mail", "displayName", "department", "createdDateTime" };
                    requestConfiguration.QueryParameters.Orderby = new[] { "createdDateTime desc" };
                }, cancellationToken);

            var appUserList = appUsers?.Value?.Select(u => new
            {
                id = u.Id,
                email = u.Mail,
                userPrincipalName = u.UserPrincipalName,
                displayName = u.DisplayName,
                department = u.Department,
                createdDateTime = u.CreatedDateTime,
                scope = "current-application"
            }).ToList();

            var allUserList = allUsers?.Value?.Select(u => new
            {
                id = u.Id,
                email = u.Mail,
                userPrincipalName = u.UserPrincipalName,
                displayName = u.DisplayName,
                department = u.Department,
                createdDateTime = u.CreatedDateTime,
                scope = u.Department?.StartsWith(applicationName) == true ? "current-application" : "other-application"
            }).ToList();

            var appUserCount = appUserList?.Count ?? 0;
            var totalUserCount = allUserList?.Count ?? 0;



            var responseAppUsers = appUserList != null ? appUserList.Cast<object>().ToList() : new List<object>();
            var responseAllUsers = allUserList != null ? allUserList.Cast<object>().ToList() : new List<object>();

            return await CreateJsonResponse(req, new
            {
                email = data.Email,
                applicationName = applicationName,
                currentApplication = new
                {
                    userCount = appUserCount,
                    users = responseAppUsers,
                    hasDuplicates = appUserCount > 1
                },
                allApplications = new
                {
                    userCount = totalUserCount,
                    users = responseAllUsers,
                    hasMultipleApplications = totalUserCount > appUserCount
                }
            }, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error listing duplicate users [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Failed to list users", correlationId);
        }
    }

    #region User Resolution Methods (Consolidated from UserResolution service)

    /// <summary>
    /// Resolves email address to Graph User ID with optional application context
    /// </summary>
    private async Task<string> ResolveEmailToUserId(string email, string? applicationId, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Resolving email {Email} to User ID for application {ApplicationId}", email, applicationId ?? "any");

            var emailFilter = $"mail eq '{email}' or userPrincipalName eq '{email}' or proxyAddresses/any(c:c eq 'SMTP:{email}')";
            var filter = emailFilter;

            if (!string.IsNullOrEmpty(applicationId))
            {
                filter = $"({emailFilter}) and startswith(department, '{applicationId}')";
            }

            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter = filter;
                    requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName", "department" };
                }, cancellationToken);

            var user = users?.Value?.FirstOrDefault();
            return user?.Id ?? string.Empty;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error resolving email {Email} to User ID: {Message}", email, ex.Message);
            return string.Empty;
        }
    }

    /// <summary>
    /// Gets user details by email with application context
    /// </summary>
    private async Task<User?> GetUserByEmail(string email, string? applicationName = null, CancellationToken cancellationToken = default)
    {
        try
        {
            _logger.LogDebug("Getting user details for email {Email} in application '{ApplicationName}'", email, applicationName ?? "any");

            var emailFilter = $"mail eq '{email}' or userPrincipalName eq '{email}' or proxyAddresses/any(c:c eq 'SMTP:{email}')";
            var filter = emailFilter;

            if (!string.IsNullOrEmpty(applicationName))
            {
                filter = $"({emailFilter}) and startswith(department, '{applicationName}')";
            }

            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter = filter;
                    requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "displayName", "department" };
                    requestConfiguration.QueryParameters.Top = 1;
                }, cancellationToken);

            return users?.Value?.FirstOrDefault();
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting user details for email {Email} in application '{ApplicationName}': {Message}",
                email, applicationName ?? "any", ex.Message);
            return null;
        }
    }

    /// <summary>
    /// Checks if user exists in specific application context
    /// </summary>
    private async Task<bool> CheckUserExistsInApplication(string email, string applicationName, CancellationToken cancellationToken)
    {
        try
        {
            _logger.LogDebug("Checking if user exists for email {Email} in application '{ApplicationName}'", email, applicationName);

            var users = await _graphServiceClient.Users
                .GetAsync(requestConfiguration =>
                {
                    requestConfiguration.QueryParameters.Filter =
                        $"(mail eq '{email}' or userPrincipalName eq '{email}' or proxyAddresses/any(c:c eq 'SMTP:{email}')) and startswith(department, '{applicationName}')";
                    requestConfiguration.QueryParameters.Select = new[] { "id", "mail", "userPrincipalName", "department" };
                    requestConfiguration.QueryParameters.Top = 1;
                }, cancellationToken);

            var exists = users?.Value?.Count > 0;
            _logger.LogDebug("User existence check for {Email} in application '{ApplicationName}': {Exists}", email, applicationName, exists);
            return exists;
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error checking user existence for {Email} in application '{ApplicationName}'", email, applicationName);
            return false;
        }
    }

    #endregion
}
