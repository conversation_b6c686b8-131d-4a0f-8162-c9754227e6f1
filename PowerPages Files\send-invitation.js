const SecureConfig = {
  getFunctionUrl(functionName = 'InvitationService') {
    const baseUrl = AZURE_FUNCTION_URL;
    return `${baseUrl}/api/${functionName}`;
  }
};

let AZURE_FUNCTION_URL = null;
let APPLICATION_NAME = null;
let currentInvitationData = null;

// Initialize configuration when DOM is ready
function initializeConfiguration() {
  // Get Azure Function URL from meta tags
  const urlMeta = document.querySelector('meta[name="azure-function-url"]');
  AZURE_FUNCTION_URL = urlMeta ? urlMeta.content : null;
  
  // Get application name from meta tags
  const appMeta = document.querySelector('meta[name="application-name"]');
  APPLICATION_NAME = appMeta ? appMeta.content : 'Osler';
  
  console.log('Configuration initialized:', {
    functionUrl: AZURE_FUNCTION_URL ? 'configured' : 'missing',
    applicationName: APPLICATION_NAME
  });
  
  if (!AZURE_FUNCTION_URL) {
    console.error('Azure Function URL not configured. Please set AzureFunctionUrl in Power Pages settings.');
    showMessage('Configuration error: Azure Function URL not set', true);
  }
}

if (!APPLICATION_NAME || APPLICATION_NAME === "ApplicationNameNotSet") {
  console.warn("Application Name not configured - check Power Pages settings");
}

const InputSanitizer = {
  sanitizeString(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  },

  validateEmail(email) {
    if (!email || typeof email !== 'string') return false;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email.trim());
  },

  validateName(name) {
    if (!name || typeof name !== 'string') return false;
    const trimmed = name.trim();
    return trimmed.length >= 1 && trimmed.length <= 50 && /^[a-zA-Z\s'-]+$/.test(trimmed);
  }
};

// DOM Elements
const errorMessage = $('#errorMessage');
const successMessage = $('#successMessage');
const invitationForm = $('#invitationForm');
const sendButton = $('#sendButton');
const recentInvitations = $('#recentInvitations');

function showMessage(message, isError = true) {
  errorMessage.addClass('d-none');
  successMessage.addClass('d-none');
  
  if (isError) {
    $('#errorText').text(message);
    errorMessage.removeClass('d-none');
  } else {
    $('#successText').text(message);
    successMessage.removeClass('d-none');
  }
}

function showLoadingState(message) {
  sendButton.prop('disabled', true).html('<span class="spinner-border spinner-border-sm" role="status" aria-hidden="true"></span> ' + message);
}

function resetLoadingState() {
  sendButton.prop('disabled', false).html('<i class="fas fa-paper-plane me-2"></i>Send Invitation');
}

async function sendInvitation(invitationData) {
  showLoadingState('Sending Invitation...');

  try {
    const sanitizedData = {
      email: InputSanitizer.sanitizeString(invitationData.email),
      firstName: InputSanitizer.sanitizeString(invitationData.firstName),
      lastName: InputSanitizer.sanitizeString(invitationData.lastName)
    };

    if (!InputSanitizer.validateEmail(sanitizedData.email)) {
      throw new Error('Invalid email format');
    }

    if (!InputSanitizer.validateName(sanitizedData.firstName)) {
      throw new Error('Invalid first name');
    }

    if (!InputSanitizer.validateName(sanitizedData.lastName)) {
      throw new Error('Invalid last name');
    }

    // Use InvitationService with invite-user operation
    const functionUrl = `${AZURE_FUNCTION_URL}/api/InvitationService?operation=invite-user`;
    
    const response = await fetch(functionUrl, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'X-Client-Version': '3.0.0-simplified',
        'X-Requested-With': 'XMLHttpRequest'
      },
      body: JSON.stringify({
        email: sanitizedData.email,
        firstName: sanitizedData.firstName,
        lastName: sanitizedData.lastName,
        applicationName: APPLICATION_NAME
      })
    });

    if (response.status === 429) {
      throw new Error('Too many requests. Please wait a moment before trying again.');
    }

    const contentType = response.headers.get('content-type');
    if (!contentType || !contentType.includes('application/json')) {
      const textResponse = await response.text();
      throw new Error(`Server error: ${response.status}`);
    }

    const result = await response.json();
    
    // Comprehensive debug logging
    console.log('=== INVITATION RESPONSE DEBUG ===');
    console.log('Response status:', response.status);
    console.log('Response ok:', response.ok);
    console.log('Response headers:', Object.fromEntries(response.headers.entries()));
    console.log('Full result object:', JSON.stringify(result, null, 2));
    console.log('Result.data:', result.data);
    console.log('Result.data.success:', result.data?.success);
    console.log('Result.success (direct):', result.success);
    console.log('================================');

    // Handle different response formats from backend:
    // Success: { data: { success: true, message: "..." }, correlationId, timestamp }
    // Rate Limit: { data: { success: false, message: "...", errorCode: "..." }, correlationId, timestamp }
    // Error: { success: false, message: "...", correlationId, timestamp }
    
    let success, message, errorCode, retryAfter;
    
    if (result.data) {
      // Wrapped format (success and rate limit responses)
      success = result.data.success;
      message = result.data.message;
      errorCode = result.data.errorCode;
      retryAfter = result.data.retryAfter;
    } else {
      // Direct format (error responses)
      success = result.success;
      message = result.message;
      errorCode = result.errorCode;
      retryAfter = result.retryAfter;
    }
    
    // Check if the operation was successful
    const isSuccess = response.ok && success === true;
    
    if (!isSuccess) {
      console.error('Response failed - Status:', response.status, 'OK:', response.ok, 'Success:', success);
      
      // Handle specific error cases
      if (response.status === 429 || errorCode === 'RateLimitExceeded') {
        const retryMessage = retryAfter ? ` Please try again after ${new Date(retryAfter).toLocaleTimeString()}.` : ' Please try again later.';
        throw new Error((message || 'Rate limit exceeded.') + retryMessage);
      }
      
      throw new Error(message || 'Failed to send invitation');
    }

    // Return success data
    return {
      success: true,
      message: message || 'Invitation sent successfully',
      correlationId: result.correlationId
    };
  } catch (error) {
    console.error('Send invitation error:', error);
    throw error;
  } finally {
    resetLoadingState();
  }
}

function validateForm() {
  let isValid = true;
  
  $('.form-control').removeClass('is-invalid');
  $('.invalid-feedback').text('');

  const email = $('#email').val();
  if (!InputSanitizer.validateEmail(email)) {
    $('#emailError').text('Please enter a valid email address');
    $('#email').addClass('is-invalid');
    isValid = false;
  }

  const firstName = $('#firstName').val();
  if (!InputSanitizer.validateName(firstName)) {
    $('#firstNameError').text('Please enter a valid first name (letters, spaces, hyphens, and apostrophes only)');
    $('#firstName').addClass('is-invalid');
    isValid = false;
  }

  const lastName = $('#lastName').val();
  if (!InputSanitizer.validateName(lastName)) {
    $('#lastNameError').text('Please enter a valid last name (letters, spaces, hyphens, and apostrophes only)');
    $('#lastName').addClass('is-invalid');
    isValid = false;
  }

  return isValid;
}

function addToRecentInvitations(email, firstName, lastName) {
  const timestamp = new Date().toLocaleString();
  const invitationHtml = `
    <div class="d-flex justify-content-between align-items-center py-1">
      <span><strong>${firstName} ${lastName}</strong> (${email})</span>
      <small class="text-muted">${timestamp}</small>
    </div>
  `;
  
  if (recentInvitations.text().includes('No recent invitations')) {
    recentInvitations.html(invitationHtml);
  } else {
    recentInvitations.prepend(invitationHtml);
  }
  
  // Keep only the last 5 invitations
  const invitations = recentInvitations.children();
  if (invitations.length > 5) {
    invitations.slice(5).remove();
  }
}

function initializeFormHandlers() {
  invitationForm.submit(async function(event) {
    event.preventDefault();
    showMessage('', false);

    try {
      if (!validateForm()) {
        return;
      }

      const invitationData = {
        email: $('#email').val(),
        firstName: $('#firstName').val(),
        lastName: $('#lastName').val()
      };

      // Store current invitation data for potential overwrite
      currentInvitationData = invitationData;

      const result = await sendInvitation(invitationData);
      
      if (result.success) {
        showMessage(`Invitation sent successfully to ${invitationData.email}!`, false);
        addToRecentInvitations(invitationData.email, invitationData.firstName, invitationData.lastName);
        invitationForm[0].reset();
        currentInvitationData = null;
      }

    } catch (error) {
      console.error('Form submission error:', error);
      showMessage(error.message || "An unexpected error occurred. Please try again.");
    }
  });
}

// Note: Overwrite functionality removed - backend now automatically invalidates previous tokens

$(document).ready(function() {
  initializeConfiguration();
  initializeFormHandlers();
});
