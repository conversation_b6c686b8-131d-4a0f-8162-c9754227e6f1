<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
<link rel="stylesheet" href="power-pages-styles.css">
<link rel="stylesheet" href="forgot-password.css">

<!-- Secure configuration and user data extraction -->
<script>
  // Secure configuration management - get from Power Pages settings
  window.appConfig = {
    functionUrl: "{{ settings['AzureFunctionUrl'] }}" || null,
    msalClientId: "{{ settings['MSALClientId'] }}" || null,
    msalTenantId: "{{ settings['MSALTenantId'] }}" || null,
    applicationName: "{{ settings['ApplicationName'] }}" || "ApplicationNameNotSet" // Enhanced: Application name
  };

  // Secure user data extraction with input sanitization
  function sanitizeUserInput(input) {
    if (typeof input !== 'string') return '';
    return input.trim().replace(/[<>\"'&]/g, '').substring(0, 256);
  }

  // Store in secure global object
  window.liquidUser = {
    applicationName: window.appConfig.applicationName
  };

  // Secure debug logging (only in development)
  if (window.location.hostname === 'localhost' || window.location.hostname.includes('dev')) {
    console.log("Debug - Forgot password page loaded:", {
      hasConfig: !!window.appConfig.functionUrl,
      applicationName: window.appConfig.applicationName
    });
  }
</script>

<div class="col-lg-12 columnBlockLayout">
<div class="row sectionBlockLayout text-start">
  <div class="container container-flex">
    <div class="container forgot-password-container">
      <h2>Reset Your Password</h2>
      <p>Enter your email address and we'll send you a link to reset your password.</p>
      <div id="errorMessage" class="alert alert-danger d-none"></div>
      <div id="successMessage" class="alert alert-success d-none"></div>
      
      <form id="forgotPasswordForm">

        
        <!-- Forgot password fields -->
        
        <!-- Email field -->
        <div class="form-group mb-3">
          <label for="email" class="form-label fw-bold">Email Address</label>
          <input type="email" id="email" required class="form-control" placeholder="Enter your email address" autocomplete="username">
          <div class="form-text">We'll send password reset instructions to this email address.</div>
          <div class="invalid-feedback"></div>
        </div>
        
        <button type="submit" id="resetButton" class="btn btn-primary">Send Reset Link</button>

      </form>
      
      <!-- Navigation Links -->
      <div class="text-center mt-3">
        <p><a href="/" class="btn btn-outline-secondary">Back to Home</a></p>
        <p>Don't have an account? <a href="/register">Create one here</a></p>
      </div>
    </div>
  </div>
</div>
</div>

<!-- Include the forgot password JavaScript file -->
<script src="/forgot-password.js"></script>
