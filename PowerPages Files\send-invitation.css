.send-invitation-container {
    max-width: 1200px;
}

.send-invitation-container .card-header h3 {
    display: flex;
    align-items: center;
    justify-content: center;
}

.send-invitation-container .card-header h3 i {
    margin-right: 0.75rem;
    font-size: 1.5rem;
}

.send-invitation-container .card-body {
    padding: 2.5rem;
}

#sendButton {
    width: 100%;
    padding: 14px 24px;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    border: none;
    border-radius: var(--osler-radius-md);
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: var(--osler-font-primary);
    text-transform: uppercase;
}

.border-top {
    border-top: 1px solid var(--osler-gray-border) !important;
    padding-top: 1.5rem !important;
    margin-top: 1.5rem !important;
}

#recentInvitations {
    font-size: 0.875rem;
    color: var(--osler-gray-medium);
    line-height: 1.5;
    padding: 1rem;
    background-color: var(--osler-gray-light);
    border-radius: var(--osler-radius-md);
    border-left: 3px solid var(--osler-red);
}

.recent-invitation-item {
    padding: 0.75rem;
    margin-bottom: 0.5rem;
    background-color: var(--osler-gray-light);
    border-radius: var(--osler-radius-sm);
    border-left: 3px solid var(--osler-red);
}

.recent-invitation-item .invitation-status.sent {
    background-color: #dbeafe;
    color: #1e40af;
}

.recent-invitation-item .invitation-status.error {
    background-color: var(--osler-red-light);
    color: var(--osler-red);
}

.col-md-10 {
    padding-left: 1rem;
    padding-right: 1rem;
}

.row.justify-content-center {
    margin-left: 0;
    margin-right: 0;
}

#sendButton.loading::after {
    content: '';
    position: absolute;
    width: 20px;
    height: 20px;
    top: 50%;
    left: 50%;
    margin-left: -10px;
    margin-top: -10px;
    border: 2px solid transparent;
    border-top-color: var(--osler-white);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
