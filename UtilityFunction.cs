using Microsoft.Azure.Functions.Worker;
using Microsoft.Azure.Functions.Worker.Http;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Options;
using System.Net;
using System.Text.Json;
using PasswordHistoryValidator.Services;
using PasswordHistoryValidator.Shared;
using Azure.Storage.Blobs;

namespace PasswordHistoryValidator;

public class UtilityFunction : BaseFunctionService
{
    private readonly ILogger<UtilityFunction> _logger;
    private readonly IPasswordHistoryService _passwordHistoryService;
    private readonly RateLimitHelper _rateLimitHelper;
    private readonly IConfiguration _configuration;
    private readonly BlobServiceClient _blobServiceClient;
    private readonly EntraOptions _entraOptions;

    public UtilityFunction(
        ILogger<UtilityFunction> logger,
        IPasswordHistoryService passwordHistoryService,
        RateLimitHelper rateLimitHelper,
        IConfiguration configuration,
        BlobServiceClient blobServiceClient,
        IOptions<EntraOptions> entraOptions,
        JsonSerializerOptions jsonOptions) : base(jsonOptions)
    {
        _logger = logger;
        _passwordHistoryService = passwordHistoryService;
        _rateLimitHelper = rateLimitHelper;
        _configuration = configuration;
        _blobServiceClient = blobServiceClient;
        _entraOptions = entraOptions.Value;
    }

    [Function("UtilityService")]
    public async Task<HttpResponseData> Run(
        [HttpTrigger(AuthorizationLevel.Anonymous, "post", "options", Route = null)] HttpRequestData req,
        FunctionContext executionContext,
        CancellationToken cancellationToken)
    {
        var correlationId = Guid.NewGuid().ToString("N")[..8];
        var logger = executionContext.GetLogger<UtilityFunction>();
        
        try
        {
            logger.LogInformation("Utility service triggered [CorrelationId: {CorrelationId}]", correlationId);
            logger.LogInformation("Request URL: {Url} [CorrelationId: {CorrelationId}]", req.Url, correlationId);
            logger.LogInformation("Request Method: {Method} [CorrelationId: {CorrelationId}]", req.Method, correlationId);
            

            if (req.Method.Equals("OPTIONS", StringComparison.OrdinalIgnoreCase))
            {
                logger.LogInformation("Handling OPTIONS request [CorrelationId: {CorrelationId}]", correlationId);
                return CreateCorsResponse(req);
            }


            var query = System.Web.HttpUtility.ParseQueryString(req.Url.Query);
            var operation = query["operation"] ?? string.Empty;
            
            logger.LogInformation("Processing operation: {Operation} [CorrelationId: {CorrelationId}]", operation, correlationId);


            var response = operation.ToLowerInvariant() switch
            {
                "health" => await HandleHealthCheck(req, correlationId, cancellationToken),
                "config-check" => await HandleConfigurationCheck(req, correlationId, cancellationToken),
                "cleanup-tokens" => await HandleTokenCleanup(req, correlationId, cancellationToken),
                "stats" => await HandleSystemStats(req, correlationId, cancellationToken),
                "test-graph" => await HandleGraphApiTest(req, correlationId, cancellationToken),
                _ => await CreateErrorResponse(req, $"Invalid operation: {operation}", correlationId)
            };
            
            logger.LogInformation("Operation {Operation} completed [CorrelationId: {CorrelationId}]", operation, correlationId);
            return response;
        }
        catch (Exception ex)
        {
            logger.LogError(ex, "Utility service error [CorrelationId: {CorrelationId}]", correlationId);
            return await CreateErrorResponse(req, "Service error", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleHealthCheck(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var healthStatus = new
            {
                status = "healthy",
                timestamp = DateTime.UtcNow,
                version = "3.0.0-simplified",
                services = new
                {
                    passwordHistoryService = await CheckPasswordHistoryServiceHealth(cancellationToken),
                    rateLimitingService = _rateLimitHelper.GetHealthStatus(),
                    configurationService = "healthy", // Simplified - DI validates config on startup
                    blobStorage = await CheckBlobStorageHealth(cancellationToken)
                },
                configuration = new
                {
                    maxHistoryCount = _configuration.GetValue<int>("PasswordHistory:MaxCount", 12),
                    workFactor = _configuration.GetValue<int>("PasswordHistory:WorkFactor", 12),
                    rateLimitPerMinute = _configuration.GetValue<int>("RateLimit:MaxRequestsPerMinute", 60),
                    hasSendGridKey = !string.IsNullOrEmpty(_configuration["SendGrid:ApiKey"]),
                    functionsRuntime = _configuration["FUNCTIONS_WORKER_RUNTIME"] ?? "not-set"
                }
            };

            return await CreateJsonResponse(req, healthStatus, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Health check error");
            return await CreateErrorResponse(req, "Health check failed", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleConfigurationCheck(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var configStatus = new
            {
                isValid = true, // Simplified - DI validates config on startup
                missingSettings = new List<string>(), // Empty - strongly-typed options handle this
                timestamp = DateTime.UtcNow,
                recommendations = new List<string> { "Configuration is managed via strongly-typed options" }
            };

            return await CreateJsonResponse(req, configStatus, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during configuration check");
            return await CreateErrorResponse(req, "Configuration check failed", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleTokenCleanup(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var cleanupResults = await CleanupExpiredTokens(cancellationToken);

            return await CreateJsonResponse(req, new
            {
                message = "Token cleanup completed",
                tokensRemoved = cleanupResults.TokensRemoved,
                tokensProcessed = cleanupResults.TokensProcessed,
                timestamp = DateTime.UtcNow
            }, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token cleanup");
            return await CreateErrorResponse(req, "Token cleanup failed", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleSystemStats(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {
            var stats = await GetSystemStatistics(cancellationToken);

            return await CreateJsonResponse(req, stats, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system stats");
            return await CreateErrorResponse(req, "Failed to get system statistics", correlationId);
        }
    }

    private async Task<HttpResponseData> HandleGraphApiTest(HttpRequestData req, string correlationId, CancellationToken cancellationToken)
    {
        try
        {

            var tenantId = _entraOptions.TenantId;
            var clientId = _entraOptions.ClientId;
            var clientSecret = _entraOptions.ClientSecret;

            var testResult = new
            {
                configurationStatus = new
                {
                    tenantIdConfigured = !string.IsNullOrEmpty(tenantId),
                    clientIdConfigured = !string.IsNullOrEmpty(clientId),
                    clientSecretConfigured = !string.IsNullOrEmpty(clientSecret),
                    tenantIdValue = string.IsNullOrEmpty(tenantId) ? "NOT_SET" : $"{tenantId.Substring(0, 8)}...",
                    clientIdValue = string.IsNullOrEmpty(clientId) ? "NOT_SET" : $"{clientId.Substring(0, 8)}..."
                },
                graphApiTest = "NOT_TESTED",
                error = null as string
            };


            if (string.IsNullOrEmpty(tenantId) || string.IsNullOrEmpty(clientId) || string.IsNullOrEmpty(clientSecret))
            {
                testResult = testResult with
                {
                    graphApiTest = "SKIPPED_MISSING_CONFIG",
                    error = "Missing Entra External ID configuration"
                };
                return await CreateJsonResponse(req, testResult, HttpStatusCode.OK, correlationId);
            }


            try
            {
                var credential = new Azure.Identity.ClientSecretCredential(tenantId, clientId, clientSecret);
                var graphServiceClient = new Microsoft.Graph.GraphServiceClient(credential);


                var organization = await graphServiceClient.Organization.GetAsync(cancellationToken: cancellationToken);

                testResult = testResult with
                {
                    graphApiTest = "SUCCESS",
                    error = null
                };


            }
            catch (Exception graphEx)
            {
                _logger.LogError(graphEx, "Graph API test failed");
                testResult = testResult with
                {
                    graphApiTest = "FAILED",
                    error = graphEx.Message
                };
            }

            return await CreateJsonResponse(req, testResult, HttpStatusCode.OK, correlationId);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during Graph API test");
            return await CreateErrorResponse(req, "Graph API test failed", correlationId);
        }
    }

    private static bool IsValidEmail(string email)
    {
        try
        {
            var addr = new System.Net.Mail.MailAddress(email);
            return addr.Address == email;
        }
        catch
        {
            return false;
        }
    }



    private async Task<string> CheckPasswordHistoryServiceHealth(CancellationToken cancellationToken)
    {
        try
        {

            var testResult = await _passwordHistoryService.ValidatePasswordAgainstHistoryAsync(
                "health-check", "test-user", "test-password-that-should-not-exist", cancellationToken);

            return testResult.IsSuccess ? "healthy" : "degraded";
        }
        catch
        {
            return "unhealthy";
        }
    }



    private async Task<string> CheckBlobStorageHealth(CancellationToken cancellationToken)
    {
        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient("passwordhistory");
            await containerClient.CreateIfNotExistsAsync(cancellationToken: cancellationToken);
            return "healthy";
        }
        catch
        {
            return "unhealthy";
        }
    }



    private async Task<CleanupResults> CleanupExpiredTokens(CancellationToken cancellationToken)
    {
        var results = new CleanupResults();

        try
        {
            var containerClient = _blobServiceClient.GetBlobContainerClient("resettokens");

            await foreach (var blobItem in containerClient.GetBlobsAsync(cancellationToken: cancellationToken))
            {
                results.TokensProcessed++;

                try
                {
                    var blobClient = containerClient.GetBlobClient(blobItem.Name);
                    var downloadResult = await blobClient.DownloadContentAsync(cancellationToken);
                    var tokenDataJson = downloadResult.Value.Content.ToString();
                    var tokenData = JsonSerializer.Deserialize<ResetTokenData>(tokenDataJson, JsonOptions);

                    if (tokenData != null && (tokenData.ExpiresUtc < DateTime.UtcNow || tokenData.Used))
                    {
                        await blobClient.DeleteIfExistsAsync(cancellationToken: cancellationToken);
                        results.TokensRemoved++;
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogWarning(ex, "Error processing token blob {BlobName} during cleanup", blobItem.Name);
                }
            }
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error during token cleanup");
            throw;
        }

        return results;
    }

    private async Task<object> GetSystemStatistics(CancellationToken cancellationToken)
    {
        try
        {
            var passwordHistoryContainer = _blobServiceClient.GetBlobContainerClient("passwordhistory");
            var resetTokensContainer = _blobServiceClient.GetBlobContainerClient("resettokens");

            var passwordHistoryCount = 0;
            var resetTokensCount = 0;
            var activeTokensCount = 0;


            await foreach (var blobItem in passwordHistoryContainer.GetBlobsAsync(cancellationToken: cancellationToken))
            {
                passwordHistoryCount++;
            }


            await foreach (var blobItem in resetTokensContainer.GetBlobsAsync(cancellationToken: cancellationToken))
            {
                resetTokensCount++;

                try
                {
                    var blobClient = resetTokensContainer.GetBlobClient(blobItem.Name);
                    var downloadResult = await blobClient.DownloadContentAsync(cancellationToken);
                    var tokenDataJson = downloadResult.Value.Content.ToString();
                    var tokenData = JsonSerializer.Deserialize<ResetTokenData>(tokenDataJson, JsonOptions);

                    if (tokenData != null && !tokenData.Used && tokenData.ExpiresUtc > DateTime.UtcNow)
                    {
                        activeTokensCount++;
                    }
                }
                catch
                {

                }
            }

            return new
            {
                passwordHistoryEntries = passwordHistoryCount,
                totalResetTokens = resetTokensCount,
                activeResetTokens = activeTokensCount,
                expiredTokens = resetTokensCount - activeTokensCount,
                timestamp = DateTime.UtcNow
            };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting system statistics");
            throw;
        }
    }



    private class CleanupResults
    {
        public int TokensProcessed { get; set; }
        public int TokensRemoved { get; set; }
    }
}
